import { Router } from "express";
import affiliate from "./affiliate";
import auth from "./auth";
import corporate from "./corporate";
import delivery from "./delivery";
import employees from "./employees";
import externalsystems from "./externalsystems";
import orders from "./orders";
import pricelist from "./pricelist";
import pricelistaffiliate from "./pricelistaffiliate";
import pricelistretailer from "./pricelistretailer";
import pricelistproduct from "./pricelistproduct";
import products from "./products";
import productsposition from "./productsposition";
import registry from "./registry";
import retailers from "./retailers";
import supplying from "./supplying";
import supplyingproduct from "./supplyingproduct";
import timeconsumingop from "./timeconsumingop";
import user from "./user";
import warehouses from "./warehouses";
import warehousescomposition from "./warehousescomposition";
import multimedia from "./multimedia";
import destination from "./destination";
import productspackaging from "./productpackaging";
import statistic from "./statistic";
import documents from "./documents";
import documentsbody from "./documentsbody";
import tasks from "./tasks";
import uploads from "./uploads"
import userguiinhibition from "./userguiinhibition"
import alyante from "./alyante"
import coperama from "./coperama"
import mrwine from "./mrwine"
import test from "./test"
import notify from "./notify"
import log from "./log"
import email from "./email"
import paymentmethods from "./paymentmethods"
import supplyingaffiliate from "./supplyingaffiliate"
import productsavailability from "./productsavailability"
import supplyingagreements from "./supplyingagreement"
import notifyuser from "./notifyuser"
import companyLookup from "./company-lookup"

const routes = Router();

// Health check endpoint
routes.get("/health", (req, res) => {
  res.json({
    status: "ok",
    timestamp: new Date().toISOString(),
    services: {
      database: "ok",
      backend: "ok"
    },
    version: "1.0.0"
  });
});

routes.use("/affiliate", affiliate);
routes.use("/auth", auth);
routes.use("/corporate", corporate);
routes.use("/delivery", delivery)
routes.use("/employees", employees);
routes.use("/externalsystems", externalsystems);
routes.use("/orders", orders);
routes.use("/pricelist", pricelist);
routes.use("/pricelistaffiliate", pricelistaffiliate);
routes.use("/pricelistretailer", pricelistretailer);
routes.use("/pricelistproduct", pricelistproduct);
routes.use("/products", products);
routes.use("/productsposition", productsposition);
routes.use("/registry", registry);
routes.use("/retailers", retailers);
routes.use("/supplying", supplying);
routes.use("/supplyingproduct", supplyingproduct);
routes.use("/timeconsumingop", timeconsumingop);
routes.use("/user", user);
routes.use("/warehouses", warehouses);
routes.use("/warehousescomp", warehousescomposition);
routes.use("/documents", documents);
routes.use("/documentsbody", documentsbody);
routes.use("/tasks", tasks);

routes.use("/destination", destination);
routes.use("/productspackaging", productspackaging);

routes.use("/multimedia", multimedia);

routes.use("/statistic", statistic);

routes.use("/uploads", uploads);

routes.use("/inhibition", userguiinhibition)

routes.use("/alyante", alyante)
routes.use("/coperama", coperama)
routes.use("/mrwine", mrwine)
routes.use("/notify", notify)
routes.use("/log", log)

routes.use("/test", test)
routes.use("/email", email)
routes.use("/paymentmethods", paymentmethods)

routes.use("/supplyingaffiliate", supplyingaffiliate)
routes.use("/supplyingagreements", supplyingagreements)

routes.use("/notifyuser", notifyuser)

routes.use("/productsavailability", productsavailability);

// Company lookup endpoint
routes.use("/company-lookup", companyLookup);

export default routes;
