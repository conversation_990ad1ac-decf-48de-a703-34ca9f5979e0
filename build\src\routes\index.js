"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const affiliate_1 = __importDefault(require("./affiliate"));
const auth_1 = __importDefault(require("./auth"));
const corporate_1 = __importDefault(require("./corporate"));
const delivery_1 = __importDefault(require("./delivery"));
const employees_1 = __importDefault(require("./employees"));
const externalsystems_1 = __importDefault(require("./externalsystems"));
const orders_1 = __importDefault(require("./orders"));
const pricelist_1 = __importDefault(require("./pricelist"));
const pricelistaffiliate_1 = __importDefault(require("./pricelistaffiliate"));
const pricelistretailer_1 = __importDefault(require("./pricelistretailer"));
const pricelistproduct_1 = __importDefault(require("./pricelistproduct"));
const products_1 = __importDefault(require("./products"));
const productsposition_1 = __importDefault(require("./productsposition"));
const registry_1 = __importDefault(require("./registry"));
const retailers_1 = __importDefault(require("./retailers"));
const supplying_1 = __importDefault(require("./supplying"));
const supplyingproduct_1 = __importDefault(require("./supplyingproduct"));
const timeconsumingop_1 = __importDefault(require("./timeconsumingop"));
const user_1 = __importDefault(require("./user"));
const warehouses_1 = __importDefault(require("./warehouses"));
const warehousescomposition_1 = __importDefault(require("./warehousescomposition"));
const multimedia_1 = __importDefault(require("./multimedia"));
const destination_1 = __importDefault(require("./destination"));
const productpackaging_1 = __importDefault(require("./productpackaging"));
const statistic_1 = __importDefault(require("./statistic"));
const documents_1 = __importDefault(require("./documents"));
const documentsbody_1 = __importDefault(require("./documentsbody"));
const tasks_1 = __importDefault(require("./tasks"));
const uploads_1 = __importDefault(require("./uploads"));
const userguiinhibition_1 = __importDefault(require("./userguiinhibition"));
const alyante_1 = __importDefault(require("./alyante"));
const coperama_1 = __importDefault(require("./coperama"));
const mrwine_1 = __importDefault(require("./mrwine"));
const test_1 = __importDefault(require("./test"));
const notify_1 = __importDefault(require("./notify"));
const log_1 = __importDefault(require("./log"));
const email_1 = __importDefault(require("./email"));
const paymentmethods_1 = __importDefault(require("./paymentmethods"));
const supplyingaffiliate_1 = __importDefault(require("./supplyingaffiliate"));
const productsavailability_1 = __importDefault(require("./productsavailability"));
const supplyingagreement_1 = __importDefault(require("./supplyingagreement"));
const notifyuser_1 = __importDefault(require("./notifyuser"));
const company_lookup_1 = __importDefault(require("./company-lookup"));
const routes = (0, express_1.Router)();
// Health check endpoint
routes.get("/health", (req, res) => {
    res.json({
        status: "ok",
        timestamp: new Date().toISOString(),
        services: {
            database: "ok",
            backend: "ok"
        },
        version: "1.0.0"
    });
});
routes.use("/affiliate", affiliate_1.default);
routes.use("/auth", auth_1.default);
routes.use("/corporate", corporate_1.default);
routes.use("/delivery", delivery_1.default);
routes.use("/employees", employees_1.default);
routes.use("/externalsystems", externalsystems_1.default);
routes.use("/orders", orders_1.default);
routes.use("/pricelist", pricelist_1.default);
routes.use("/pricelistaffiliate", pricelistaffiliate_1.default);
routes.use("/pricelistretailer", pricelistretailer_1.default);
routes.use("/pricelistproduct", pricelistproduct_1.default);
routes.use("/products", products_1.default);
routes.use("/productsposition", productsposition_1.default);
routes.use("/registry", registry_1.default);
routes.use("/retailers", retailers_1.default);
routes.use("/supplying", supplying_1.default);
routes.use("/supplyingproduct", supplyingproduct_1.default);
routes.use("/timeconsumingop", timeconsumingop_1.default);
routes.use("/user", user_1.default);
routes.use("/warehouses", warehouses_1.default);
routes.use("/warehousescomp", warehousescomposition_1.default);
routes.use("/documents", documents_1.default);
routes.use("/documentsbody", documentsbody_1.default);
routes.use("/tasks", tasks_1.default);
routes.use("/destination", destination_1.default);
routes.use("/productspackaging", productpackaging_1.default);
routes.use("/multimedia", multimedia_1.default);
routes.use("/statistic", statistic_1.default);
routes.use("/uploads", uploads_1.default);
routes.use("/inhibition", userguiinhibition_1.default);
routes.use("/alyante", alyante_1.default);
routes.use("/coperama", coperama_1.default);
routes.use("/mrwine", mrwine_1.default);
routes.use("/notify", notify_1.default);
routes.use("/log", log_1.default);
routes.use("/test", test_1.default);
routes.use("/email", email_1.default);
routes.use("/paymentmethods", paymentmethods_1.default);
routes.use("/supplyingaffiliate", supplyingaffiliate_1.default);
routes.use("/supplyingagreements", supplyingagreement_1.default);
routes.use("/notifyuser", notifyuser_1.default);
routes.use("/productsavailability", productsavailability_1.default);
// Company lookup endpoint
routes.use("/company-lookup", company_lookup_1.default);
exports.default = routes;
//# sourceMappingURL=index.js.map