{"name": "@edge-runtime/primitives", "description": "A set of primitives to build Vercel Edge Runtime.", "homepage": "https://edge-runtime.vercel.app/packages/primitives", "version": "4.1.0", "main": "dist/index.js", "repository": {"directory": "packages/primitives", "type": "git", "url": "git+https://github.com/vercel/edge-runtime.git"}, "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "keywords": ["apis", "edge", "edge-runtime", "functions", "primites", "runtime", "standard", "web"], "devDependencies": {"@ungap/structured-clone": "1.2.0", "blob-polyfill": "7.0.20220408", "esbuild-plugin-alias": "latest", "event-target-shim": "6.0.2", "tsup": "8", "undici": "5.23.0", "urlpattern-polyfill": "10.0.0", "@edge-runtime/format": "2.2.1"}, "engines": {"node": ">=16"}, "files": ["dist", "load", "types"], "license": "MPL-2.0", "publishConfig": {"access": "public"}, "types": "types/index.d.ts", "scripts": {"build": "ts-node scripts/build.ts", "clean:build": "rm -rf dist abort-controller blob console crypto events fetch streams structured-clone url", "clean:node": "rm -rf node_modules", "prebuild": "pnpm run clean:build"}}