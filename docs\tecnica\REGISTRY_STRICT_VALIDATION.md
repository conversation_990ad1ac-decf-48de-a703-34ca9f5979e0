# 🔒 Validazioni Restrittive Registry

Documentazione delle nuove validazioni restrittive implementate per la gestione delle anagrafiche Registry.

---

## ✅ **NUOVE REGOLE IMPLEMENTATE**

### **1. Email Obbligatoria**
- ✅ **Campo obbligatorio**: L'email è ora richiesta per tutte le anagrafiche
- ✅ **Validazione formato**: Deve essere un indirizzo email valido
- ✅ **Messaggio specifico**: "Email è obbligatoria" se mancante

```json
// Errore se email mancante
{
  "error": "Errori di validazione",
  "message": "Email è obbligatoria",
  "details": [{
    "field": "email",
    "fieldName": "Email",
    "message": "Email è obbligatoria"
  }],
  "code": "VALIDATION_ERROR"
}
```

### **2. Almeno Un Contatto Telefonico Obbligatorio**
- ✅ **Regola**: Almeno uno tra cellulare e telefono fisso deve essere compilato
- ✅ **Gestione concatenazione**: I numeri vengono concatenati con "/" intelligentemente
- ✅ **Validazione formato**: Solo numeri, spazi, +, -, parentesi (6-20 caratteri)

```json
// Errore se entrambi i telefoni mancanti
{
  "error": "Errori di validazione", 
  "message": "È obbligatorio inserire almeno un numero di telefono (cellulare o fisso)",
  "details": [{
    "field": "tel",
    "fieldName": "Contatti Telefonici",
    "message": "È obbligatorio inserire almeno un numero di telefono (cellulare o fisso)"
  }],
  "code": "VALIDATION_ERROR"
}
```

---

## 🔧 **LOGICA DI CONCATENAZIONE TELEFONI**

### **Input Frontend → Backend:**
```javascript
// Dati inviati dal frontend
{
  "cellnum": "************",    // Cellulare
  "telnum": "06 1234 5678"     // Telefono fisso
}
```

### **Concatenazione Intelligente:**
```javascript
// Caso 1: Entrambi presenti
cellnum: "************", telnum: "06 1234 5678"
→ tel: "************/06 1234 5678"

// Caso 2: Solo cellulare
cellnum: "************", telnum: ""
→ tel: "************/"

// Caso 3: Solo telefono fisso  
cellnum: "", telnum: "06 1234 5678"
→ tel: "/06 1234 5678"

// Caso 4: Entrambi vuoti (ERRORE)
cellnum: "", telnum: ""
→ VALIDATION_ERROR
```

---

## 📋 **VALIDAZIONI COMPLETE**

### **Campi Obbligatori:**
1. ✅ **Ragione Sociale** (`firstName`)
2. ✅ **Email** (`email`) - **NUOVO**
3. ✅ **Almeno un telefono** (`cellnum` OR `telnum`) - **NUOVO**

### **Validazioni Formato:**
1. ✅ **Email**: Formato email valido (<EMAIL>)
2. ✅ **Partita IVA**: Esattamente 11 cifre
3. ✅ **CAP**: Esattamente 5 cifre
4. ✅ **Telefoni**: Solo numeri, spazi, +, -, parentesi (6-20 caratteri) - **NUOVO**

### **Validazioni Database:**
1. ✅ **P.IVA Unica**: Non può esistere duplicato nello stesso Corporate
2. ✅ **Corporate Valido**: Deve esistere nel database
3. ✅ **Lunghezza Campi**: Rispetto limiti database

---

## 🎯 **ESEMPI PRATICI**

### **✅ Dati Validi:**
```json
{
  "firstName": "Azienda Test SRL",
  "email": "<EMAIL>",
  "cellnum": "************",
  "telnum": "",
  "pIva": "12345678901",
  "address": "Via Roma 123",
  "city": "Roma",
  "cap": "00100"
}
```

### **❌ Dati Non Validi:**
```json
{
  "firstName": "",                    // ❌ Obbligatorio
  "email": "",                        // ❌ Obbligatorio  
  "cellnum": "",                      // ❌ Almeno uno richiesto
  "telnum": "",                       // ❌ Almeno uno richiesto
  "pIva": "123",                      // ❌ Deve essere 11 cifre
  "cap": "123"                        // ❌ Deve essere 5 cifre
}
```

---

## 🔄 **COMPATIBILITÀ**

### **Metodi Interessati:**
- ✅ **POST /registry** (creazione) - Validazioni complete
- ✅ **PUT /registry** (modifica) - Validazioni su campi modificati
- ✅ **POST /registry/anonimous** - Validazioni complete

### **Backward Compatibility:**
- ✅ **Risposte successo**: Identiche a prima
- ✅ **Struttura dati**: Nessuna breaking change
- ⚠️ **Validazioni più restrittive**: Il frontend deve gestire i nuovi errori

---

## 🎨 **GESTIONE FRONTEND**

### **Esempio Gestione Errori:**
```javascript
try {
  const response = await api.post('/registry', registryData);
  // Successo
} catch (error) {
  if (error.response.status === 400) {
    const { details } = error.response.data;
    
    details.forEach(fieldError => {
      switch(fieldError.field) {
        case 'email':
          showFieldError('email', fieldError.message);
          break;
        case 'tel':
          showFieldError('phone', fieldError.message);
          break;
        case 'cellnum':
          showFieldError('cellnum', fieldError.message);
          break;
        case 'telnum':
          showFieldError('telnum', fieldError.message);
          break;
      }
    });
  }
}
```

### **Validazione Lato Client Suggerita:**
```javascript
function validateRegistry(data) {
  const errors = [];
  
  if (!data.firstName?.trim()) {
    errors.push('Ragione Sociale è obbligatoria');
  }
  
  if (!data.email?.trim()) {
    errors.push('Email è obbligatoria');
  }
  
  if (!data.cellnum?.trim() && !data.telnum?.trim()) {
    errors.push('Almeno un numero di telefono è obbligatorio');
  }
  
  return errors;
}
```

---

## 🎉 **BENEFICI**

1. ✅ **Dati più completi**: Email e telefono sempre presenti
2. ✅ **Comunicazione garantita**: Possibilità di contattare sempre l'anagrafica
3. ✅ **Qualità dati**: Validazioni più rigorose
4. ✅ **UX migliorata**: Errori specifici e actionable
5. ✅ **Consistenza**: Regole uniformi per creazione e modifica

---

**Implementato**: 16/07/2025  
**Versione**: 2.3.0
