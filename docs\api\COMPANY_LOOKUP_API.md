# 🔍 Company Lookup API - Ricerca P.IVA con VIES

Documentazione dell'endpoint per la ricerca automatica dei dati aziendali tramite Partita IVA utilizzando il servizio VIES dell'Unione Europea.

---

## 📡 **ENDPOINT**

### **GET /company-lookup**

Ricerca i dati aziendali tramite Partita IVA utilizzando il servizio VIES (VAT Information Exchange System).

#### **URL Completo:**
```
GET http://localhost:3001/company-lookup?vat={partitaIVA}
```

#### **Esempio Chiamata:**
```bash
GET /company-lookup?vat=*************
```

---

## 🔐 **AUTENTICAZIONE**

- ✅ **Richiesta**: JWT Token nell'header Authorization
- ✅ **Header**: `Authorization: Bearer {token}`
- ✅ **Ruoli**: Tutti i ruoli autenticati

---

## 📥 **PARAMETRI**

### **Query Parameters:**

| Parametro | Tipo | Obbligatorio | Descrizione | Esempio |
|-----------|------|--------------|-------------|---------|
| `vat` | string | ✅ | Partita IVA italiana (IT + 11 cifre) | `*************` |

### **Validazioni Input:**
- ✅ **Formato**: `IT` + esattamente 11 cifre numeriche
- ✅ **Pulizia automatica**: Spazi rimossi automaticamente
- ✅ **Case insensitive**: `it` convertito in `IT`

---

## 📤 **RISPOSTE**

### **✅ Successo (200)**
```json
{
  "success": true,
  "company": {
    "name": "AMAZON ITALIA SERVICES SRL",
    "vatNumber": "*************",
    "address": "VIALE MONTE GRAPPA 3/5",
    "city": "MILANO MI",
    "postalCode": "20124",
    "country": "IT",
    "isValid": true
  },
  "source": "VIES"
}
```

### **❌ P.IVA Non Trovata (404)**
```json
{
  "success": false,
  "error": {
    "code": "VAT_NOT_FOUND",
    "message": "Nessuna azienda trovata con questa Partita IVA"
  }
}
```

### **❌ Formato Non Valido (400)**
```json
{
  "success": false,
  "error": {
    "code": "INVALID_VAT_FORMAT",
    "message": "Formato Partita IVA non valido. Utilizzare il formato IT + 11 cifre (es: *************)"
  }
}
```

### **❌ Parametro Mancante (400)**
```json
{
  "success": false,
  "error": {
    "code": "MISSING_VAT_PARAMETER",
    "message": "Il parametro vat è obbligatorio"
  }
}
```

### **❌ Servizio Non Disponibile (503)**
```json
{
  "success": false,
  "error": {
    "code": "SERVICE_UNAVAILABLE",
    "message": "Il servizio di lookup P.IVA è temporaneamente non disponibile"
  }
}
```

### **❌ Timeout (503)**
```json
{
  "success": false,
  "error": {
    "code": "SERVICE_TIMEOUT",
    "message": "Il servizio di lookup P.IVA ha impiegato troppo tempo a rispondere. Riprovare."
  }
}
```

### **❌ Errore Interno (500)**
```json
{
  "success": false,
  "error": {
    "code": "INTERNAL_ERROR",
    "message": "Errore interno del server durante il lookup P.IVA"
  }
}
```

---

## 🔧 **SPECIFICHE TECNICHE**

### **Servizio Esterno:**
- **Provider**: VIES (VAT Information Exchange System) - Unione Europea
- **URL**: `https://ec.europa.eu/taxation_customs/vies/rest-api/check-vat-number`
- **Gratuito**: ✅ Servizio pubblico UE
- **Rate Limiting**: Gestito dal servizio VIES

### **Configurazioni:**
- **Timeout**: 10 secondi
- **User-Agent**: `EP-Backend/1.0.0`
- **Retry**: Nessun retry automatico (gestito dal frontend)

### **Parsing Indirizzo:**
- **Input**: `"VIALE MONTE GRAPPA 3/5, MILANO MI"`
- **Output**: 
  - `address`: `"VIALE MONTE GRAPPA 3/5"`
  - `city`: `"MILANO MI"`
  - `postalCode`: `""` (estratto se presente)

---

## 💻 **ESEMPI DI UTILIZZO**

### **JavaScript/Axios:**
```javascript
try {
  const response = await axios.get('/company-lookup', {
    params: { vat: '*************' },
    headers: { 'Authorization': `Bearer ${token}` }
  });
  
  if (response.data.success) {
    const company = response.data.company;
    console.log(`Azienda: ${company.name}`);
    console.log(`Indirizzo: ${company.address}, ${company.city}`);
  }
} catch (error) {
  if (error.response.status === 404) {
    console.log('P.IVA non trovata');
  } else if (error.response.status === 503) {
    console.log('Servizio temporaneamente non disponibile');
  }
}
```

### **cURL:**
```bash
curl -X GET "http://localhost:3001/company-lookup?vat=*************" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

---

## 🎯 **GESTIONE ERRORI FRONTEND**

### **Codici di Stato:**
```javascript
switch (error.response.status) {
  case 400:
    // Formato P.IVA non valido o parametro mancante
    showError('Verificare il formato della Partita IVA');
    break;
  case 404:
    // P.IVA non trovata
    showWarning('Nessuna azienda trovata con questa P.IVA');
    break;
  case 503:
    // Servizio non disponibile
    showError('Servizio temporaneamente non disponibile. Riprovare.');
    break;
  case 500:
    // Errore interno
    showError('Errore interno. Contattare il supporto.');
    break;
}
```

### **Auto-completamento Form:**
```javascript
async function lookupCompany(vat) {
  try {
    const response = await api.get('/company-lookup', { params: { vat } });
    
    if (response.data.success) {
      const company = response.data.company;
      
      // Auto-compila il form
      document.getElementById('companyName').value = company.name;
      document.getElementById('address').value = company.address;
      document.getElementById('city').value = company.city;
      document.getElementById('postalCode').value = company.postalCode;
      document.getElementById('vatNumber').value = company.vatNumber;
    }
  } catch (error) {
    handleLookupError(error);
  }
}
```

---

## 🚀 **BENEFICI**

1. ✅ **Auto-completamento**: Compilazione automatica dei dati aziendali
2. ✅ **Validazione**: Verifica automatica della validità della P.IVA
3. ✅ **Accuratezza**: Dati ufficiali dal database europeo VIES
4. ✅ **UX Migliorata**: Riduce errori di digitazione
5. ✅ **Compliance**: Verifica ufficiale per normative fiscali
6. ✅ **Gratuito**: Nessun costo aggiuntivo (servizio UE pubblico)

---

## ⚠️ **LIMITAZIONI**

1. **Solo P.IVA Italiane**: Attualmente supporta solo formato `IT{11cifre}`
2. **Dipendenza Esterna**: Funzionalità dipende dalla disponibilità del servizio VIES
3. **Rate Limiting**: Possibili limitazioni dal servizio VIES
4. **Timeout**: Richieste che superano 10 secondi vengono interrotte

---

**Implementato**: 16/07/2025  
**Versione**: 2.4.0  
**Servizio**: VIES (VAT Information Exchange System)
