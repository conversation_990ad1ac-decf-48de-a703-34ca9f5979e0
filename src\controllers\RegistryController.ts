/**
 * Central Unit acl-fe-api
 * 2020 - Viniexport.com (C)
 *
 * RegistryController - operazioni su affiliati
 *
 */
import { Request, Response } from "express";
import { getConnection, getRepository, In, Not } from "typeorm";
import { Affiliate } from "../entity/Affiliate";
import { Corporate } from "../entity/Corporate";
import { Destination } from "../entity/Destination";
import { Registry } from "../entity/Registry";
import { Retailers } from "../entity/Retailers";
import { User } from "../entity/User";
import { validate } from "class-validator";
import { PriceListAffiliate } from "../entity/PriceListAffiliate";
import { PriceList } from "../entity/PriceList";
import { sendMailNewRegistration } from "./SenderMailController";
class RegistryController {

  static create = async (req: Request, res: Response) => {
    const id = res.locals.jwtPayload.userId;

    getRepository(User)
      .findOne(id, { relations: ["idRegistry", "idRegistry.idCorporate"] })
      .then(async (user) => {
        let {
          firstName,
          lastName,
          address,
          city,
          cap,
          pIva,
          email,
          cellnum,
          telnum,
          paymentMetod,
        } = req.body;

        // Validazioni specifiche per AGENTI
        if (user.role === "AGENTE") {
          // Gli AGENTI possono creare Registry solo nel proprio Corporate
          if (req.body.idCorporate && req.body.idCorporate !== user.idRegistry.idCorporate.id) {
            res.status(403).json({
              error: "Accesso negato",
              message: "Gli AGENTI possono creare PDV solo nel proprio Corporate"
            });
            return;
          }

          // Validazioni obbligatorie per AGENTI
          if (!firstName || !pIva) {
            res.status(400).json({
              error: "Dati mancanti",
              message: "Ragione sociale (firstName) e P.IVA sono obbligatori per gli AGENTI"
            });
            return;
          }

          // Validazione formato P.IVA italiana (11 cifre)
          const pIvaRegex = /^[0-9]{11}$/;
          if (!pIvaRegex.test(pIva.replace(/\s/g, ""))) {
            res.status(400).json({
              error: "P.IVA non valida",
              message: "La P.IVA deve essere composta da 11 cifre"
            });
            return;
          }

          // Verifica che non esista già un registry con la stessa P.IVA nel Corporate
          const existingRegistry = await getRepository(Registry).findOne({
            where: {
              pIva: pIva,
              idCorporate: user.idRegistry.idCorporate
            }
          });

          if (existingRegistry) {
            res.status(409).json({
              error: "PDV già esistente",
              message: `Esiste già un PDV con P.IVA ${pIva} in questo Corporate`
            });
            return;
          }
        }

        let registry = new Registry();
        registry.firstName = firstName;
        registry.lastName = lastName;
        registry.address = address;
        registry.city = city;
        registry.cap = cap;
        registry.pIva = pIva;
        registry.email = email;
        registry.tel = cellnum + "/" + telnum;
        registry.isValid = true;
        registry.paymentMetod =
          paymentMetod != "" ? paymentMetod : "CONTANTE ALLO SCARICO";
        registry.idCorporate = req.body.idCorporate ? req.body.idCorporate : user.idRegistry.idCorporate;

        //Validade if the parameters are ok
        const errors = await validate(registry);
        if (errors.length > 0) {
          const errorMessages = errors.map(error => ({
            field: error.property,
            constraints: error.constraints,
            message: Object.values(error.constraints || {}).join(', ')
          }));

          res.status(400).json({
            error: "Errori di validazione",
            message: "I dati forniti non sono validi",
            details: errorMessages,
            code: "VALIDATION_ERROR"
          });
          return;
        }

        await getRepository(Registry)
          .save(registry)
          .then((registry) => {
            res.status(200).send(registry);
            return;
          })
          .catch(async (e) => {
            if (e.code === "23505") {
              // Errore di duplicato (unique constraint violation)
              console.log("Duplicate key error:", e.code);

              res.status(409).json({
                error: "Registry già esistente",
                message: `Esiste già un Registry con P.IVA ${registry.pIva} in questo Corporate`,
                code: "DUPLICATE_REGISTRY",
                details: {
                  pIva: registry.pIva,
                  corporate: registry.idCorporate
                }
              });
              return;
            } else {
              // Altri errori del database
              console.error("Database error:", e);

              res.status(500).json({
                error: "Errore interno del server",
                message: "Si è verificato un errore durante il salvataggio del Registry",
                code: "DATABASE_ERROR",
                details: process.env.NODE_ENV === 'development' ? e.message : undefined
              });
              return;
            }

          });
      });
  };

  static edit = async (req: Request, res: Response) => {
    const id = res.locals.jwtPayload.userId;

    getRepository(User)
      .findOneOrFail(id, { relations: ["idRegistry", "idRegistry.idCorporate"] })
      .then(async (user) => {
        var idRegistry: number = parseInt(req.query.idRegistry.toString());

        // Trova il Registry da modificare
        const existingRegistry = await getRepository(Registry).findOne(idRegistry, {
          relations: ["idCorporate"]
        });

        if (!existingRegistry) {
          res.status(404).json({
            error: "Registry non trovato",
            message: "Il Registry specificato non esiste"
          });
          return;
        }

        // Validazioni specifiche per AGENTI
        if (user.role === "AGENTE") {
          // Gli AGENTI possono modificare Registry solo del proprio Corporate
          if (existingRegistry.idCorporate.id !== user.idRegistry.idCorporate.id) {
            res.status(403).json({
              error: "Accesso negato",
              message: "Gli AGENTI possono modificare solo Registry del proprio Corporate"
            });
            return;
          }

          // Se viene modificata la P.IVA, validarla
          if (req.body.pIva && req.body.pIva !== existingRegistry.pIva) {
            // Validazione formato P.IVA italiana (11 cifre)
            const pIvaRegex = /^[0-9]{11}$/;
            if (!pIvaRegex.test(req.body.pIva.replace(/\s/g, ""))) {
              res.status(400).json({
                error: "P.IVA non valida",
                message: "La P.IVA deve essere composta da 11 cifre"
              });
              return;
            }

            // Verifica che non esista già un altro registry con la stessa P.IVA nel Corporate
            const duplicateRegistry = await getRepository(Registry).findOne({
              where: {
                pIva: req.body.pIva,
                idCorporate: user.idRegistry.idCorporate,
                id: Not(idRegistry) // Esclude il registry corrente
              }
            });

            if (duplicateRegistry) {
              res.status(409).json({
                error: "P.IVA già esistente",
                message: `Esiste già un altro Registry con P.IVA ${req.body.pIva} in questo Corporate`
              });
              return;
            }
          }

          // Gli AGENTI non possono cambiare il Corporate
          if (req.body.idCorporate && req.body.idCorporate !== existingRegistry.idCorporate.id) {
            res.status(403).json({
              error: "Operazione non consentita",
              message: "Gli AGENTI non possono modificare il Corporate di appartenenza"
            });
            return;
          }
        }

        let registry = new Registry();
        registry = req.body;
        registry.updateAt = new Date();

        await getRepository(Registry)
          .update(
            {
              id: idRegistry,
            },
            registry
          )
          .then(() => {
            res.status(200).send("modifica avvenuta");
            return;
          })
          .catch((e) => {
            console.error("Error updating registry:", e);

            if (e.code === "23505") {
              res.status(409).json({
                error: "Conflitto dati",
                message: "I dati forniti sono in conflitto con un Registry esistente",
                code: "DUPLICATE_DATA",
                details: process.env.NODE_ENV === 'development' ? e.detail : undefined
              });
            } else {
              res.status(500).json({
                error: "Errore interno del server",
                message: "Si è verificato un errore durante la modifica del Registry",
                code: "UPDATE_ERROR",
                details: process.env.NODE_ENV === 'development' ? e.message : undefined
              });
            }
            return;
          });
      })
      .catch((err) => {
        console.error("Error in edit method:", err);

        res.status(500).json({
          error: "Errore interno del server",
          message: "Si è verificato un errore durante l'elaborazione della richiesta",
          code: "INTERNAL_ERROR",
          details: process.env.NODE_ENV === 'development' ? err.message : undefined
        });
        return;
      })
  };

  static delete = async (req: Request, res: Response) => {
    res.status(201).send("cancellazione non implementata");
  };

  static get = async (req: Request, res: Response, uniqueConstraints: any) => {
    console.log(uniqueConstraints);
    const id = res.locals.jwtPayload.userId;

    getRepository(User)
      .findOne(id, { relations: ["idRegistry", "idRegistry.idCorporate"] })
      .then(async (user) => {
        await getRepository(Registry)
          .find({ where: { idCorporate: user.idRegistry.idCorporate } })
          .then(async (registry) => {
            var ids = registry.map((element) => element.id);

            try {
              const registryRepository = getRepository(Registry);

              if (req.query.id != undefined) {
                let id: number = parseInt(req.query.id.toString());

                registryRepository
                  .findOne(id, {
                    where: {
                      id: In(ids),
                    },
                    relations: ["users"],
                  })
                  .then((registry) => {
                    if (!registry) {
                      res.status(404).json({
                        error: "Registry non trovato",
                        message: "Il Registry specificato non esiste o non hai i permessi per accedervi",
                        code: "REGISTRY_NOT_FOUND"
                      });
                      return;
                    }
                    res.status(200).send(registry);
                  })
                  .catch((e) => {
                    console.error("Error fetching registry by ID:", e);
                    res.status(500).json({
                      error: "Errore interno del server",
                      message: "Si è verificato un errore durante il recupero del Registry",
                      code: "FETCH_ERROR",
                      details: process.env.NODE_ENV === 'development' ? e.message : undefined
                    });
                    return;
                  });
              } else {
                registryRepository
                  .find({
                    where: {
                      id: In(ids),
                    },
                    relations: ["users"],
                  })
                  .then((registry) => {
                    res.status(200).send(registry);
                  })
                  .catch((e) => {
                    console.error("Error fetching registries:", e);
                    res.status(500).json({
                      error: "Errore interno del server",
                      message: "Si è verificato un errore durante il recupero dei Registry",
                      code: "FETCH_ERROR",
                      details: process.env.NODE_ENV === 'development' ? e.message : undefined
                    });
                    return;
                  });
              }
            } catch (errore) {
              console.error("Unexpected error in get method:", errore);
              res.status(500).json({
                error: "Errore interno del server",
                message: "Si è verificato un errore imprevisto durante l'elaborazione della richiesta",
                code: "UNEXPECTED_ERROR",
                details: process.env.NODE_ENV === 'development' ? errore.message : undefined
              });
              return;
            }
          });
      });
  };

  static anonimous = async (req: Request, res: Response) => {

    const connection = getConnection();
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    let result = []

    let { registry, user } = req.body;
    let idUserCreated: number = 0

    registry.tel = registry.cellnum + "/" + registry.telnum;
    registry.isValid = true;
    registry.paymentMetod = registry.paymentMetod != "" ? registry.paymentMetod : "CONTANTE ALLO SCARICO";

    try {
      await queryRunner.manager.save(Registry, registry)
        .then(async registry_created => {

          result.push("Registry saved id: " + registry_created.id);

          let user_new = new User();
          user_new.username = user.username;
          user_new.password = user.password;
          user_new.role = "AFFILIATO";
          user_new.idRegistry = registry_created.id;

          //Validade if the parameters are ok
          const errors = await validate(user_new);
          if (errors.length > 0) {
            result.push("User invalid " + errors)
          }

          user_new.hashPassword();


          await queryRunner.manager.save(User, user_new)
            .then(async user_created => {

              result.push("User saved id: " + user_created.id);
              idUserCreated = user_created.id

              let affiliate = new Affiliate();
              affiliate.idRegistry = registry_created.id;


              affiliate = await queryRunner.manager.save(Affiliate, affiliate)

              let pricelistaffiliate = new PriceListAffiliate();
              pricelistaffiliate.idAffiliate = affiliate.id;
              pricelistaffiliate.idPriceList = await (await getRepository(PriceList).findOne()).id

              await queryRunner.manager.save(PriceListAffiliate, pricelistaffiliate)
                .then(async () => {

                  result.push("PriceList " + pricelistaffiliate.idPriceList + " of Affiliate " + pricelistaffiliate.idAffiliate + " saved");
                  await queryRunner.commitTransaction();
                  await sendMailNewRegistration(user_created.idRegistry.email, "<EMAIL>", "Welcome", idUserCreated, "", req.headers.host)
                })
                .catch(e => {
                  result.push("PriceList Affiliate not Saved " + e)
                })
              //invio notifica mail
            })
            .catch(async err => {
              await queryRunner.rollbackTransaction();
              result.push("User not saved " + err);
            })


        })
    }
    catch (err) {
      await queryRunner.rollbackTransaction();
      result.push("Registry not saved " + err)
    } finally {
      await queryRunner.release();
      res.status(200).send(result)


    }

  };

  static async insert(registryArray: [any], message: string) {
    return await new Promise(async (resolve, reject) => {
      switch (message) {
        case "insertRegistryAlyante":
          for (const entity of registryArray) {
            console.log("check");
            if (entity.agente == undefined) {
              var cellnum =
                entity.anagrafica.cellnum != undefined
                  ? entity.anagrafica.cellnum + "/"
                  : "-/";
              var telnum =
                entity.anagrafica.tel1Num != undefined
                  ? entity.anagrafica.tel1Num
                  : "-";

              let registryPdv = new Registry();
              registryPdv.firstName = entity.anagrafica.ragioneSociale;
              registryPdv.lastName = "";
              registryPdv.address =
                entity.anagrafica.indirizzi[0] != undefined
                  ? entity.anagrafica.indirizzi[0].indirizzoCompleto
                  : "";
              registryPdv.city =
                entity.anagrafica.indirizzi[0] != undefined
                  ? entity.anagrafica.indirizzi[0].citta
                  : "";
              registryPdv.cap =
                entity.anagrafica.indirizzi[0] != undefined
                  ? entity.anagrafica.indirizzi[0].cap
                  : "";
              registryPdv.pIva =
                entity.anagrafica.partiva != undefined
                  ? entity.anagrafica.partiva
                  : entity.anagrafica.codiceFiscale;
              registryPdv.email = entity.anagrafica.indemail;
              registryPdv.tel = cellnum + telnum;
              registryPdv.isValid = true;
              registryPdv.paymentMetod =
                entity.condizionePagamentoCO != undefined
                  ? entity.condizionePagamentoCO.descPag
                  : "CONTANTE ALLO SCARICO";
              registryPdv.externalCode = entity.anagrafica.codice;
              registryPdv.idCorporate = await getRepository(Corporate).findOne({
                where: { corporateName: "Viniexport" },
              });

              await getRepository(Registry)
                .save(registryPdv)
                .catch(async () => {
                  await getRepository(Registry)
                    .update(
                      {
                        pIva: registryPdv.pIva,
                        idCorporate: registryPdv.idCorporate,
                      },
                      registryPdv
                    )
                    .catch((e) => {
                      console.error(
                        "ERRORE: REGISTRY NON SALVABILE NE AGGIORNABILE pIva: %s, idCorporate: %s, idExternalSystem: %s",
                        registryPdv.pIva,
                        registryPdv.idCorporate,
                      );
                      console.log(e);
                    });

                  if (entity.destinationArray) {
                    await getRepository(Registry)
                      .findOne({
                        where: {
                          pIva: registryPdv.pIva,
                          idCorporate: registryPdv.idCorporate,
                        },
                      })
                      .then(async (registry) => {
                        for (const dest of entity.destinationArray) {
                          let destination = new Destination();
                          destination = dest;
                          destination.idRegistry = registry;
                          await getRepository(Destination)
                            .save(destination)
                            .catch((e) => {
                              console.error(
                                this.toString() + "  Destinazione non salvabile"
                              );
                            });
                        }
                      })
                      .catch((e) => {
                        console.log("nessun registry trovato");
                      });
                  }

                  resolve("Creazione avvenuta");
                });
            } else {
              var cellnum =
                entity.anagrafica.cellnum != undefined
                  ? entity.anagrafica.cellnum + "/"
                  : "-/";
              var telnum =
                entity.anagrafica.tel1Num != undefined
                  ? entity.anagrafica.tel1Num
                  : "-";

              let registryPdv = new Registry();
              registryPdv.firstName = entity.anagrafica.ragioneSociale;
              registryPdv.lastName = "";
              registryPdv.address =
                entity.anagrafica.indirizzi[0] != undefined
                  ? entity.anagrafica.indirizzi[0].indirizzoCompleto
                  : "";
              registryPdv.city =
                entity.anagrafica.indirizzi[0] != undefined
                  ? entity.anagrafica.indirizzi[0].citta
                  : "";
              registryPdv.cap =
                entity.anagrafica.indirizzi[0] != undefined
                  ? entity.anagrafica.indirizzi[0].cap
                  : "";
              registryPdv.pIva =
                entity.anagrafica.partiva != undefined
                  ? entity.anagrafica.partiva
                  : entity.anagrafica.codiceFiscale;
              registryPdv.email = entity.anagrafica.indemail;
              registryPdv.tel = cellnum + telnum;
              registryPdv.isValid = true;
              registryPdv.paymentMetod =
                entity.condizionePagamentoCO != undefined
                  ? entity.condizionePagamentoCO.descPag
                  : "CONTANTE ALLO SCARICO";
              registryPdv.externalCode = entity.anagrafica.codice;

              await getRepository(Registry)
                .save(registryPdv)
                .catch(async () => {
                  await getRepository(Registry)
                    .update(
                      {
                        pIva: registryPdv.pIva,
                        idCorporate: registryPdv.idCorporate,
                      },
                      registryPdv
                    )
                    .catch((e) => {
                      console.error(
                        "ERRORE: REGISTRY NON SALVABILE NE AGGIORNABILE pIva: %s, idCorporate: %s, idExternalSystem: %s",
                        registryPdv.pIva,
                        registryPdv.idCorporate,
                      );
                      console.log(e);
                    });
                });

              if (entity.destinationArray) {
                await getRepository(Registry)
                  .findOne({
                    where: {
                      pIva: registryPdv.pIva,
                      idCorporate: registryPdv.idCorporate,
                    },
                  })
                  .then(async (registry) => {
                    for (const dest of entity.destinationArray) {
                      let destination = new Destination();
                      destination = dest;
                      destination.idRegistry = registry;
                      await getRepository(Destination)
                        .save(destination)
                        .catch((e) => {
                          console.error(
                            this.toString() + "  Destinazione non salvabile"
                          );
                        });
                    }
                  })
                  .catch((e) => {
                    console.log("nessun registry trovato");
                  });
              }

              cellnum =
                entity.agente.anagraficaGeneraleCO.cellnum != undefined
                  ? entity.agente.anagraficaGeneraleCO.cellnum + "/"
                  : "-/";
              telnum =
                entity.agente.anagraficaGeneraleCO.tel1Num != undefined
                  ? entity.agente.anagraficaGeneraleCO.tel1Num
                  : "-";

              let registryAgente = new Registry();

              registryAgente.firstName =
                entity.agente.anagraficaGeneraleCO.ragioneSociale;
              registryAgente.lastName = "";
              registryAgente.address =
                entity.agente.anagraficaGeneraleCO.indirizzoCO;
              registryAgente.city = entity.agente.anagraficaGeneraleCO.citta;
              registryAgente.cap = entity.agente.anagraficaGeneraleCO.cap;
              registryAgente.pIva =
                entity.agente.anagraficaGeneraleCO.partiva != undefined
                  ? entity.agente.anagraficaGeneraleCO.partiva
                  : entity.agente.anagraficaGeneraleCO.codiceFiscale;
              registryAgente.email =
                entity.agente.anagraficaGeneraleCO.indemail;
              registryAgente.tel = cellnum + telnum;
              registryAgente.isValid = true;
              registryAgente.externalCode =
                entity.agente.anagraficaGeneraleCO.codice;
              registryAgente.idCorporate = await getRepository(
                Corporate
              ).findOne({ where: { corporateName: "Viniexport" } });

              await getRepository(Registry)
                .save(registryAgente)
                .catch(async () => {
                  await getRepository(Registry)
                    .update(
                      {
                        pIva: registryAgente.pIva,
                        idCorporate: registryAgente.idCorporate,
                      },
                      registryAgente
                    )
                    .catch((e) => {
                      console.error(
                        "ERRORE: REGISTRY NON SALVABILE NE AGGIORNABILE pIva: %s, idCorporate: %s, idExternalSystem: %s",
                        registryAgente.pIva,
                        registryAgente.idCorporate,
                      );
                      console.log(e);
                    });
                });

              var agenteReg = await getRepository(Registry).findOne({
                where: {
                  pIva: registryAgente.pIva,
                  idCorporate: registryAgente.idCorporate,
                },
              });
              var affiliato = new Affiliate();
              affiliato.idRegistry = agenteReg.id;
              await getRepository(Affiliate)
                .save(affiliato)
                .catch((e) => console.error(e));

              affiliato = await getRepository(Affiliate).findOne({
                where: {
                  idRegistry: agenteReg,
                },
              });

              var pdvReg = await getRepository(Registry).findOne({
                where: {
                  pIva: registryPdv.pIva,
                  idCorporate: registryPdv.idCorporate,
                },
              });

              let retailers = new Retailers();
              retailers.idAffiliate = affiliato.id;
              retailers.idRegistry = pdvReg;

              await getRepository(Retailers)
                .save(retailers)
                .catch((e) => console.error(e));

              resolve("Creazione avvenuta");
            }
          }
          break;

        default:
          reject("nessun sistema corrispondente");
          break;
      }
    });
  }
}

export default RegistryController;
