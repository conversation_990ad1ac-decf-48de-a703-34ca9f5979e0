# 🔌 API Documentation

Documentazione completa degli endpoint API del backend EP-Backend.

---

## 📋 **ENDPOINT DISPONIBILI**

### **🔍 Company Lookup API**
- **File**: [COMPANY_LOOKUP_API.md](COMPANY_LOOKUP_API.md)
- **Endpoint**: `GET /company-lookup?vat={partitaIVA}`
- **Descrizione**: Ricerca automatica dati aziendali tramite P.IVA usando servizio VIES
- **Autenticazione**: JWT token richiesto
- **Servizio**: VIES (VAT Information Exchange System) - UE

---

## 🎯 **COME USARE QUESTA DOCUMENTAZIONE**

### **Per Sviluppatori Frontend:**
1. Consulta la documentazione specifica dell'endpoint
2. Verifica i parametri richiesti e formati
3. Controlla i codici di risposta e gestione errori
4. Usa gli esempi di chiamata forniti

### **Per Testing:**
1. Usa Swagger UI: http://localhost:3001/api-docs
2. Testa con Postman usando gli esempi forniti
3. Verifica autenticazione JWT per endpoint protetti

### **Per Integrazioni:**
1. Controlla rate limiting e timeout
2. Implementa gestione errori appropriata
3. Considera cache per ottimizzazioni

---

## 🔗 **RISORSE CORRELATE**

- [📁 Implementazioni](../implementazioni/) - Dettagli tecnici implementazione
- [📁 Sviluppo](../sviluppo/) - Guide sviluppo API
- [📁 Testing](../testing/) - Test API e copertura
- [🌐 Swagger UI](http://localhost:3001/api-docs) - Documentazione interattiva

---

**Ultimo aggiornamento**: 16/01/2025
