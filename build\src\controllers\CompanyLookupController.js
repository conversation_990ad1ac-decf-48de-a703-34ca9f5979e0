"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
const axios_1 = __importDefault(require("axios"));
class CompanyLookupController {
    /**
     * Valida il formato della Partita IVA italiana
     */
    static validateItalianVAT(vat) {
        // Rimuovi spazi e converti in maiuscolo
        const cleanVat = vat.replace(/\s/g, '').toUpperCase();
        // Verifica formato IT + 11 cifre
        const italianVatRegex = /^IT([0-9]{11})$/;
        const match = cleanVat.match(italianVatRegex);
        if (!match) {
            return { isValid: false };
        }
        return {
            isValid: true,
            countryCode: 'IT',
            vatNumber: match[1]
        };
    }
    /**
     * Parsing dell'indirizzo VIES per estrarre città e CAP
     */
    static parseAddress(address) {
        if (!address) {
            return { address: '', city: '', postalCode: '' };
        }
        // Pattern comuni per indirizzi italiani
        // Esempio: "VIALE MONTE GRAPPA 3/5, MILANO MI" o "VIA ROMA 123, 00100 ROMA"
        const addressParts = address.split(',').map(part => part.trim());
        if (addressParts.length >= 2) {
            const streetAddress = addressParts[0];
            const cityPart = addressParts[addressParts.length - 1];
            // Cerca CAP (5 cifre) nella parte città
            const postalCodeMatch = cityPart.match(/(\d{5})/);
            const postalCode = postalCodeMatch ? postalCodeMatch[1] : '';
            // Rimuovi CAP dalla città se presente
            const city = cityPart.replace(/\d{5}\s*/, '').trim();
            return {
                address: streetAddress,
                city: city,
                postalCode: postalCode
            };
        }
        return {
            address: address,
            city: '',
            postalCode: ''
        };
    }
    /**
     * Chiamata al servizio VIES per il lookup P.IVA
     */
    static async callVIESService(countryCode, vatNumber) {
        var _b, _c;
        const VIES_REST_URL = 'https://ec.europa.eu/taxation_customs/vies/rest-api/check-vat-number';
        try {
            console.log(`🔍 Calling VIES service for ${countryCode}${vatNumber}`);
            const response = await axios_1.default.get(VIES_REST_URL, {
                params: {
                    countryCode: countryCode,
                    vatNumber: vatNumber
                },
                timeout: 10000, // 10 secondi timeout
                headers: {
                    'User-Agent': 'EP-Backend/1.0.0',
                    'Accept': 'application/json'
                }
            });
            console.log('✅ VIES response received:', response.data);
            return response.data;
        }
        catch (error) {
            console.error('❌ VIES service error:', error.message);
            if (error.code === 'ECONNABORTED') {
                throw new Error('TIMEOUT');
            }
            else if (((_b = error.response) === null || _b === void 0 ? void 0 : _b.status) === 400) {
                throw new Error('INVALID_REQUEST');
            }
            else if (((_c = error.response) === null || _c === void 0 ? void 0 : _c.status) >= 500) {
                throw new Error('SERVICE_UNAVAILABLE');
            }
            else {
                throw new Error('NETWORK_ERROR');
            }
        }
    }
}
_a = CompanyLookupController;
/**
 * Endpoint principale per il lookup P.IVA
 * GET /company-lookup?vat={partitaIVA}
 */
CompanyLookupController.lookup = async (req, res) => {
    try {
        const { vat } = req.query;
        // Validazione parametro
        if (!vat || typeof vat !== 'string') {
            res.status(400).json({
                success: false,
                error: {
                    code: 'MISSING_VAT_PARAMETER',
                    message: 'Il parametro vat è obbligatorio'
                }
            });
            return;
        }
        // Validazione formato P.IVA italiana
        const validation = _a.validateItalianVAT(vat);
        if (!validation.isValid) {
            res.status(400).json({
                success: false,
                error: {
                    code: 'INVALID_VAT_FORMAT',
                    message: 'Formato Partita IVA non valido. Utilizzare il formato IT + 11 cifre (es: *************)'
                }
            });
            return;
        }
        // Chiamata al servizio VIES
        try {
            const viesResponse = await _a.callVIESService(validation.countryCode, validation.vatNumber);
            // P.IVA non valida o non trovata
            if (!viesResponse.valid) {
                res.status(404).json({
                    success: false,
                    error: {
                        code: 'VAT_NOT_FOUND',
                        message: 'Nessuna azienda trovata con questa Partita IVA'
                    }
                });
                return;
            }
            // Parsing indirizzo
            const addressInfo = _a.parseAddress(viesResponse.address || '');
            // Risposta di successo
            res.status(200).json({
                success: true,
                company: {
                    name: viesResponse.name || '',
                    vatNumber: `${validation.countryCode}${validation.vatNumber}`,
                    address: addressInfo.address,
                    city: addressInfo.city,
                    postalCode: addressInfo.postalCode,
                    country: validation.countryCode,
                    isValid: viesResponse.valid
                },
                source: 'VIES'
            });
        }
        catch (serviceError) {
            console.error('🚨 VIES service error:', serviceError.message);
            switch (serviceError.message) {
                case 'TIMEOUT':
                    res.status(503).json({
                        success: false,
                        error: {
                            code: 'SERVICE_TIMEOUT',
                            message: 'Il servizio di lookup P.IVA ha impiegato troppo tempo a rispondere. Riprovare.'
                        }
                    });
                    break;
                case 'SERVICE_UNAVAILABLE':
                    res.status(503).json({
                        success: false,
                        error: {
                            code: 'SERVICE_UNAVAILABLE',
                            message: 'Il servizio di lookup P.IVA è temporaneamente non disponibile'
                        }
                    });
                    break;
                case 'INVALID_REQUEST':
                    res.status(400).json({
                        success: false,
                        error: {
                            code: 'INVALID_VAT_FORMAT',
                            message: 'Formato Partita IVA non riconosciuto dal servizio VIES'
                        }
                    });
                    break;
                default:
                    res.status(500).json({
                        success: false,
                        error: {
                            code: 'LOOKUP_ERROR',
                            message: 'Errore durante la ricerca dei dati aziendali'
                        }
                    });
            }
        }
    }
    catch (error) {
        console.error('🚨 Unexpected error in company lookup:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'INTERNAL_ERROR',
                message: 'Errore interno del server durante il lookup P.IVA'
            }
        });
    }
};
exports.default = CompanyLookupController;
//# sourceMappingURL=CompanyLookupController.js.map