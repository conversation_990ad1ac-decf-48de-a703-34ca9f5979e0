"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
const request_1 = __importDefault(require("request"));
class CompanyLookupController {
    /**
     * Valida il formato della Partita IVA italiana
     */
    static validateItalianVAT(vat) {
        // Rimuovi spazi e converti in maiuscolo
        const cleanVat = vat.replace(/\s/g, '').toUpperCase();
        // Verifica formato IT + 11 cifre
        const italianVatRegex = /^IT([0-9]{11})$/;
        const match = cleanVat.match(italianVatRegex);
        if (!match) {
            return { isValid: false };
        }
        return {
            isValid: true,
            countryCode: 'IT',
            vatNumber: match[1]
        };
    }
    /**
     * Parsing dell'indirizzo VIES per estrarre città e CAP
     */
    static parseAddress(address) {
        if (!address) {
            return { address: '', city: '', postalCode: '' };
        }
        // Pulisci l'indirizzo da newlines e spazi extra
        const cleanAddress = address.replace(/\n/g, ' ').replace(/\s+/g, ' ').trim();
        // Pattern per indirizzi italiani VIES
        // Esempio: "VIALE MONTE GRAPPA 3/5 20124 MILANO MI"
        // Cerca CAP (5 cifre) nell'indirizzo
        const postalCodeMatch = cleanAddress.match(/(\d{5})/);
        const postalCode = postalCodeMatch ? postalCodeMatch[1] : '';
        if (postalCode) {
            // Dividi l'indirizzo usando il CAP come separatore
            const parts = cleanAddress.split(postalCode);
            if (parts.length >= 2) {
                const streetAddress = parts[0].trim();
                const cityPart = parts[1].trim();
                return {
                    address: streetAddress,
                    city: cityPart,
                    postalCode: postalCode
                };
            }
        }
        // Fallback: prova a dividere per virgola
        const addressParts = cleanAddress.split(',').map(part => part.trim());
        if (addressParts.length >= 2) {
            const streetAddress = addressParts[0];
            const cityPart = addressParts[addressParts.length - 1];
            // Cerca CAP nella parte città
            const postalCodeMatch = cityPart.match(/(\d{5})/);
            const postalCode = postalCodeMatch ? postalCodeMatch[1] : '';
            // Rimuovi CAP dalla città se presente
            const city = cityPart.replace(/\d{5}\s*/, '').trim();
            return {
                address: streetAddress,
                city: city,
                postalCode: postalCode
            };
        }
        // Fallback finale: restituisci tutto come indirizzo
        return {
            address: cleanAddress,
            city: '',
            postalCode: ''
        };
    }
    /**
     * Metodo per chiamate HTTP seguendo il pattern del progetto
     */
    static async apiRequestSender(method, url, body) {
        const options = {
            method: method,
            url: url,
            headers: {
                'User-Agent': 'EP-Backend/1.0.0',
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            },
            body: body,
            timeout: 10000 // 10 secondi timeout
        };
        return await new Promise((resolve, reject) => {
            (0, request_1.default)(options, function (error, response, body) {
                if (error) {
                    console.error('❌ Request error:', error.message);
                    if (error.code === 'ECONNRESET' || error.code === 'ETIMEDOUT') {
                        reject(new Error('TIMEOUT'));
                    }
                    else if (error.code === 'ECONNREFUSED') {
                        reject(new Error('SERVICE_UNAVAILABLE'));
                    }
                    else {
                        reject(new Error('NETWORK_ERROR'));
                    }
                    return;
                }
                try {
                    // Gestione codici di stato HTTP
                    if (response.statusCode === 400) {
                        reject(new Error('INVALID_REQUEST'));
                        return;
                    }
                    else if (response.statusCode >= 500) {
                        reject(new Error('SERVICE_UNAVAILABLE'));
                        return;
                    }
                    else if (response.statusCode !== 200) {
                        reject(new Error('NETWORK_ERROR'));
                        return;
                    }
                    // Parse della risposta JSON
                    const parsedBody = JSON.parse(body);
                    console.log('✅ VIES response received:', parsedBody);
                    resolve(parsedBody);
                }
                catch (parseError) {
                    console.error('❌ JSON parse error:', parseError);
                    reject(new Error('INVALID_RESPONSE'));
                }
            });
        });
    }
    /**
     * Chiamata al servizio VIES per il lookup P.IVA
     */
    static async callVIESService(countryCode, vatNumber) {
        const VIES_REST_URL = 'https://ec.europa.eu/taxation_customs/vies/rest-api/check-vat-number';
        console.log(`🔍 Calling VIES service for ${countryCode}${vatNumber}`);
        const requestBody = JSON.stringify({
            countryCode: countryCode,
            vatNumber: vatNumber
        });
        try {
            const response = await _a.apiRequestSender('POST', VIES_REST_URL, requestBody);
            return response;
        }
        catch (error) {
            console.error('❌ VIES service error:', error.message);
            throw error;
        }
    }
}
_a = CompanyLookupController;
/**
 * Endpoint principale per il lookup P.IVA
 * GET /company-lookup?vat={partitaIVA}
 */
CompanyLookupController.lookup = async (req, res) => {
    try {
        const { vat } = req.query;
        // Validazione parametro
        if (!vat || typeof vat !== 'string') {
            res.status(400).json({
                success: false,
                error: {
                    code: 'MISSING_VAT_PARAMETER',
                    message: 'Il parametro vat è obbligatorio'
                }
            });
            return;
        }
        // Validazione formato P.IVA italiana
        const validation = _a.validateItalianVAT(vat);
        if (!validation.isValid) {
            res.status(400).json({
                success: false,
                error: {
                    code: 'INVALID_VAT_FORMAT',
                    message: 'Formato Partita IVA non valido. Utilizzare il formato IT + 11 cifre (es: *************)'
                }
            });
            return;
        }
        // Chiamata al servizio VIES
        try {
            const viesResponse = await _a.callVIESService(validation.countryCode, validation.vatNumber);
            // P.IVA non valida o non trovata
            if (!viesResponse.valid) {
                res.status(404).json({
                    success: false,
                    error: {
                        code: 'VAT_NOT_FOUND',
                        message: 'Nessuna azienda trovata con questa Partita IVA'
                    }
                });
                return;
            }
            // Parsing indirizzo
            const addressInfo = _a.parseAddress(viesResponse.address || '');
            // Risposta di successo
            res.status(200).json({
                success: true,
                company: {
                    name: viesResponse.name || '',
                    vatNumber: `${validation.countryCode}${validation.vatNumber}`,
                    address: addressInfo.address,
                    city: addressInfo.city,
                    postalCode: addressInfo.postalCode,
                    country: validation.countryCode,
                    isValid: viesResponse.valid
                },
                source: 'VIES'
            });
        }
        catch (serviceError) {
            console.error('🚨 VIES service error:', serviceError.message);
            switch (serviceError.message) {
                case 'TIMEOUT':
                    res.status(503).json({
                        success: false,
                        error: {
                            code: 'SERVICE_TIMEOUT',
                            message: 'Il servizio di lookup P.IVA ha impiegato troppo tempo a rispondere. Riprovare.'
                        }
                    });
                    break;
                case 'SERVICE_UNAVAILABLE':
                    res.status(503).json({
                        success: false,
                        error: {
                            code: 'SERVICE_UNAVAILABLE',
                            message: 'Il servizio di lookup P.IVA è temporaneamente non disponibile'
                        }
                    });
                    break;
                case 'INVALID_REQUEST':
                    res.status(400).json({
                        success: false,
                        error: {
                            code: 'INVALID_VAT_FORMAT',
                            message: 'Formato Partita IVA non riconosciuto dal servizio VIES'
                        }
                    });
                    break;
                default:
                    res.status(500).json({
                        success: false,
                        error: {
                            code: 'LOOKUP_ERROR',
                            message: 'Errore durante la ricerca dei dati aziendali'
                        }
                    });
            }
        }
    }
    catch (error) {
        console.error('🚨 Unexpected error in company lookup:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'INTERNAL_ERROR',
                message: 'Errore interno del server durante il lookup P.IVA'
            }
        });
    }
};
exports.default = CompanyLookupController;
//# sourceMappingURL=CompanyLookupController.js.map