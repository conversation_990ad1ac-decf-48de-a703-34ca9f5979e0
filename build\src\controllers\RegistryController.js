"use strict";
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
const typeorm_1 = require("typeorm");
const Affiliate_1 = require("../entity/Affiliate");
const Corporate_1 = require("../entity/Corporate");
const Destination_1 = require("../entity/Destination");
const Registry_1 = require("../entity/Registry");
const Retailers_1 = require("../entity/Retailers");
const User_1 = require("../entity/User");
const class_validator_1 = require("class-validator");
const PriceListAffiliate_1 = require("../entity/PriceListAffiliate");
const PriceList_1 = require("../entity/PriceList");
const SenderMailController_1 = require("./SenderMailController");
class RegistryController {
    static async insert(registryArray, message) {
        return await new Promise(async (resolve, reject) => {
            switch (message) {
                case "insertRegistryAlyante":
                    for (const entity of registryArray) {
                        console.log("check");
                        if (entity.agente == undefined) {
                            var cellnum = entity.anagrafica.cellnum != undefined
                                ? entity.anagrafica.cellnum + "/"
                                : "-/";
                            var telnum = entity.anagrafica.tel1Num != undefined
                                ? entity.anagrafica.tel1Num
                                : "-";
                            let registryPdv = new Registry_1.Registry();
                            registryPdv.firstName = entity.anagrafica.ragioneSociale;
                            registryPdv.lastName = "";
                            registryPdv.address =
                                entity.anagrafica.indirizzi[0] != undefined
                                    ? entity.anagrafica.indirizzi[0].indirizzoCompleto
                                    : "";
                            registryPdv.city =
                                entity.anagrafica.indirizzi[0] != undefined
                                    ? entity.anagrafica.indirizzi[0].citta
                                    : "";
                            registryPdv.cap =
                                entity.anagrafica.indirizzi[0] != undefined
                                    ? entity.anagrafica.indirizzi[0].cap
                                    : "";
                            registryPdv.pIva =
                                entity.anagrafica.partiva != undefined
                                    ? entity.anagrafica.partiva
                                    : entity.anagrafica.codiceFiscale;
                            registryPdv.email = entity.anagrafica.indemail;
                            registryPdv.tel = cellnum + telnum;
                            registryPdv.isValid = true;
                            registryPdv.paymentMetod =
                                entity.condizionePagamentoCO != undefined
                                    ? entity.condizionePagamentoCO.descPag
                                    : "CONTANTE ALLO SCARICO";
                            registryPdv.externalCode = entity.anagrafica.codice;
                            registryPdv.idCorporate = await (0, typeorm_1.getRepository)(Corporate_1.Corporate).findOne({
                                where: { corporateName: "Viniexport" },
                            });
                            await (0, typeorm_1.getRepository)(Registry_1.Registry)
                                .save(registryPdv)
                                .catch(async () => {
                                await (0, typeorm_1.getRepository)(Registry_1.Registry)
                                    .update({
                                    pIva: registryPdv.pIva,
                                    idCorporate: registryPdv.idCorporate,
                                }, registryPdv)
                                    .catch((e) => {
                                    console.error("ERRORE: REGISTRY NON SALVABILE NE AGGIORNABILE pIva: %s, idCorporate: %s, idExternalSystem: %s", registryPdv.pIva, registryPdv.idCorporate);
                                    console.log(e);
                                });
                                if (entity.destinationArray) {
                                    await (0, typeorm_1.getRepository)(Registry_1.Registry)
                                        .findOne({
                                        where: {
                                            pIva: registryPdv.pIva,
                                            idCorporate: registryPdv.idCorporate,
                                        },
                                    })
                                        .then(async (registry) => {
                                        for (const dest of entity.destinationArray) {
                                            let destination = new Destination_1.Destination();
                                            destination = dest;
                                            destination.idRegistry = registry;
                                            await (0, typeorm_1.getRepository)(Destination_1.Destination)
                                                .save(destination)
                                                .catch((e) => {
                                                console.error(this.toString() + "  Destinazione non salvabile");
                                            });
                                        }
                                    })
                                        .catch((e) => {
                                        console.log("nessun registry trovato");
                                    });
                                }
                                resolve("Creazione avvenuta");
                            });
                        }
                        else {
                            var cellnum = entity.anagrafica.cellnum != undefined
                                ? entity.anagrafica.cellnum + "/"
                                : "-/";
                            var telnum = entity.anagrafica.tel1Num != undefined
                                ? entity.anagrafica.tel1Num
                                : "-";
                            let registryPdv = new Registry_1.Registry();
                            registryPdv.firstName = entity.anagrafica.ragioneSociale;
                            registryPdv.lastName = "";
                            registryPdv.address =
                                entity.anagrafica.indirizzi[0] != undefined
                                    ? entity.anagrafica.indirizzi[0].indirizzoCompleto
                                    : "";
                            registryPdv.city =
                                entity.anagrafica.indirizzi[0] != undefined
                                    ? entity.anagrafica.indirizzi[0].citta
                                    : "";
                            registryPdv.cap =
                                entity.anagrafica.indirizzi[0] != undefined
                                    ? entity.anagrafica.indirizzi[0].cap
                                    : "";
                            registryPdv.pIva =
                                entity.anagrafica.partiva != undefined
                                    ? entity.anagrafica.partiva
                                    : entity.anagrafica.codiceFiscale;
                            registryPdv.email = entity.anagrafica.indemail;
                            registryPdv.tel = cellnum + telnum;
                            registryPdv.isValid = true;
                            registryPdv.paymentMetod =
                                entity.condizionePagamentoCO != undefined
                                    ? entity.condizionePagamentoCO.descPag
                                    : "CONTANTE ALLO SCARICO";
                            registryPdv.externalCode = entity.anagrafica.codice;
                            await (0, typeorm_1.getRepository)(Registry_1.Registry)
                                .save(registryPdv)
                                .catch(async () => {
                                await (0, typeorm_1.getRepository)(Registry_1.Registry)
                                    .update({
                                    pIva: registryPdv.pIva,
                                    idCorporate: registryPdv.idCorporate,
                                }, registryPdv)
                                    .catch((e) => {
                                    console.error("ERRORE: REGISTRY NON SALVABILE NE AGGIORNABILE pIva: %s, idCorporate: %s, idExternalSystem: %s", registryPdv.pIva, registryPdv.idCorporate);
                                    console.log(e);
                                });
                            });
                            if (entity.destinationArray) {
                                await (0, typeorm_1.getRepository)(Registry_1.Registry)
                                    .findOne({
                                    where: {
                                        pIva: registryPdv.pIva,
                                        idCorporate: registryPdv.idCorporate,
                                    },
                                })
                                    .then(async (registry) => {
                                    for (const dest of entity.destinationArray) {
                                        let destination = new Destination_1.Destination();
                                        destination = dest;
                                        destination.idRegistry = registry;
                                        await (0, typeorm_1.getRepository)(Destination_1.Destination)
                                            .save(destination)
                                            .catch((e) => {
                                            console.error(this.toString() + "  Destinazione non salvabile");
                                        });
                                    }
                                })
                                    .catch((e) => {
                                    console.log("nessun registry trovato");
                                });
                            }
                            cellnum =
                                entity.agente.anagraficaGeneraleCO.cellnum != undefined
                                    ? entity.agente.anagraficaGeneraleCO.cellnum + "/"
                                    : "-/";
                            telnum =
                                entity.agente.anagraficaGeneraleCO.tel1Num != undefined
                                    ? entity.agente.anagraficaGeneraleCO.tel1Num
                                    : "-";
                            let registryAgente = new Registry_1.Registry();
                            registryAgente.firstName =
                                entity.agente.anagraficaGeneraleCO.ragioneSociale;
                            registryAgente.lastName = "";
                            registryAgente.address =
                                entity.agente.anagraficaGeneraleCO.indirizzoCO;
                            registryAgente.city = entity.agente.anagraficaGeneraleCO.citta;
                            registryAgente.cap = entity.agente.anagraficaGeneraleCO.cap;
                            registryAgente.pIva =
                                entity.agente.anagraficaGeneraleCO.partiva != undefined
                                    ? entity.agente.anagraficaGeneraleCO.partiva
                                    : entity.agente.anagraficaGeneraleCO.codiceFiscale;
                            registryAgente.email =
                                entity.agente.anagraficaGeneraleCO.indemail;
                            registryAgente.tel = cellnum + telnum;
                            registryAgente.isValid = true;
                            registryAgente.externalCode =
                                entity.agente.anagraficaGeneraleCO.codice;
                            registryAgente.idCorporate = await (0, typeorm_1.getRepository)(Corporate_1.Corporate).findOne({ where: { corporateName: "Viniexport" } });
                            await (0, typeorm_1.getRepository)(Registry_1.Registry)
                                .save(registryAgente)
                                .catch(async () => {
                                await (0, typeorm_1.getRepository)(Registry_1.Registry)
                                    .update({
                                    pIva: registryAgente.pIva,
                                    idCorporate: registryAgente.idCorporate,
                                }, registryAgente)
                                    .catch((e) => {
                                    console.error("ERRORE: REGISTRY NON SALVABILE NE AGGIORNABILE pIva: %s, idCorporate: %s, idExternalSystem: %s", registryAgente.pIva, registryAgente.idCorporate);
                                    console.log(e);
                                });
                            });
                            var agenteReg = await (0, typeorm_1.getRepository)(Registry_1.Registry).findOne({
                                where: {
                                    pIva: registryAgente.pIva,
                                    idCorporate: registryAgente.idCorporate,
                                },
                            });
                            var affiliato = new Affiliate_1.Affiliate();
                            affiliato.idRegistry = agenteReg.id;
                            await (0, typeorm_1.getRepository)(Affiliate_1.Affiliate)
                                .save(affiliato)
                                .catch((e) => console.error(e));
                            affiliato = await (0, typeorm_1.getRepository)(Affiliate_1.Affiliate).findOne({
                                where: {
                                    idRegistry: agenteReg,
                                },
                            });
                            var pdvReg = await (0, typeorm_1.getRepository)(Registry_1.Registry).findOne({
                                where: {
                                    pIva: registryPdv.pIva,
                                    idCorporate: registryPdv.idCorporate,
                                },
                            });
                            let retailers = new Retailers_1.Retailers();
                            retailers.idAffiliate = affiliato.id;
                            retailers.idRegistry = pdvReg;
                            await (0, typeorm_1.getRepository)(Retailers_1.Retailers)
                                .save(retailers)
                                .catch((e) => console.error(e));
                            resolve("Creazione avvenuta");
                        }
                    }
                    break;
                default:
                    reject("nessun sistema corrispondente");
                    break;
            }
        });
    }
}
_a = RegistryController;
RegistryController.create = async (req, res) => {
    const id = res.locals.jwtPayload.userId;
    (0, typeorm_1.getRepository)(User_1.User)
        .findOne(id, { relations: ["idRegistry", "idRegistry.idCorporate"] })
        .then(async (user) => {
        let { firstName, lastName, address, city, cap, pIva, email, cellnum, telnum, paymentMetod, } = req.body;
        // Validazioni specifiche per AGENTI
        if (user.role === "AGENTE") {
            // Gli AGENTI possono creare Registry solo nel proprio Corporate
            if (req.body.idCorporate && req.body.idCorporate !== user.idRegistry.idCorporate.id) {
                res.status(403).json({
                    error: "Accesso negato",
                    message: "Gli AGENTI possono creare PDV solo nel proprio Corporate"
                });
                return;
            }
            // Validazioni obbligatorie per AGENTI
            if (!firstName || !pIva) {
                res.status(400).json({
                    error: "Dati mancanti",
                    message: "Ragione sociale (firstName) e P.IVA sono obbligatori per gli AGENTI"
                });
                return;
            }
            // Validazione formato P.IVA italiana (11 cifre)
            const pIvaRegex = /^[0-9]{11}$/;
            if (!pIvaRegex.test(pIva.replace(/\s/g, ""))) {
                res.status(400).json({
                    error: "P.IVA non valida",
                    message: "La P.IVA deve essere composta da 11 cifre"
                });
                return;
            }
            // Verifica che non esista già un registry con la stessa P.IVA nel Corporate
            const existingRegistry = await (0, typeorm_1.getRepository)(Registry_1.Registry).findOne({
                where: {
                    pIva: pIva,
                    idCorporate: user.idRegistry.idCorporate
                }
            });
            if (existingRegistry) {
                res.status(409).json({
                    error: "PDV già esistente",
                    message: `Esiste già un PDV con P.IVA ${pIva} in questo Corporate`
                });
                return;
            }
        }
        // Validazioni preliminari specifiche
        const validationErrors = [];
        if (!firstName || firstName.trim() === '') {
            validationErrors.push({
                field: 'firstName',
                fieldName: 'Ragione Sociale',
                message: 'Ragione Sociale è obbligatoria'
            });
        }
        if (email && email.trim() !== '') {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                validationErrors.push({
                    field: 'email',
                    fieldName: 'Email',
                    message: 'Email deve essere un indirizzo valido (esempio: <EMAIL>)'
                });
            }
        }
        if (pIva && pIva.trim() !== '') {
            const cleanPIva = pIva.replace(/\s/g, '');
            if (!/^[0-9]{11}$/.test(cleanPIva)) {
                validationErrors.push({
                    field: 'pIva',
                    fieldName: 'Partita IVA',
                    message: 'Partita IVA deve essere composta da esattamente 11 cifre'
                });
            }
        }
        if (cap && cap.trim() !== '') {
            if (!/^[0-9]{5}$/.test(cap.trim())) {
                validationErrors.push({
                    field: 'cap',
                    fieldName: 'CAP',
                    message: 'CAP deve essere composto da 5 cifre'
                });
            }
        }
        // Se ci sono errori di validazione preliminare, restituiscili
        if (validationErrors.length > 0) {
            const mainMessage = validationErrors.length === 1
                ? validationErrors[0].message
                : `Errori in ${validationErrors.length} campi: ${validationErrors.map(e => e.fieldName).join(', ')}`;
            res.status(400).json({
                error: "Errori di validazione",
                message: mainMessage,
                details: validationErrors,
                code: "VALIDATION_ERROR"
            });
            return;
        }
        let registry = new Registry_1.Registry();
        registry.firstName = firstName;
        registry.lastName = lastName;
        registry.address = address;
        registry.city = city;
        registry.cap = cap;
        registry.pIva = pIva;
        registry.email = email;
        registry.tel = cellnum + "/" + telnum;
        registry.isValid = true;
        registry.paymentMetod =
            paymentMetod != "" ? paymentMetod : "CONTANTE ALLO SCARICO";
        registry.idCorporate = req.body.idCorporate ? req.body.idCorporate : user.idRegistry.idCorporate;
        //Validade if the parameters are ok
        const errors = await (0, class_validator_1.validate)(registry);
        if (errors.length > 0) {
            const errorMessages = errors.map(error => {
                const fieldName = error.property;
                const constraints = error.constraints || {};
                // Traduci i nomi dei campi in italiano
                const fieldTranslations = {
                    'firstName': 'Ragione Sociale',
                    'lastName': 'Cognome',
                    'email': 'Email',
                    'pIva': 'Partita IVA',
                    'address': 'Indirizzo',
                    'city': 'Città',
                    'cap': 'CAP',
                    'tel': 'Telefono'
                };
                const translatedField = fieldTranslations[fieldName] || fieldName;
                // Crea messaggi più user-friendly
                let userFriendlyMessage = '';
                if (constraints.isNotEmpty) {
                    userFriendlyMessage = `${translatedField} è obbligatorio`;
                }
                else if (constraints.isEmail) {
                    userFriendlyMessage = `${translatedField} deve essere un indirizzo email valido`;
                }
                else if (constraints.length) {
                    userFriendlyMessage = `${translatedField} deve avere la lunghezza corretta`;
                }
                else if (constraints.matches) {
                    userFriendlyMessage = `${translatedField} ha un formato non valido`;
                }
                else {
                    userFriendlyMessage = Object.values(constraints).join(', ');
                }
                return {
                    field: fieldName,
                    fieldName: translatedField,
                    message: userFriendlyMessage,
                    constraints: constraints
                };
            });
            const mainMessage = errorMessages.length === 1
                ? errorMessages[0].message
                : `Errori in ${errorMessages.length} campi: ${errorMessages.map(e => e.fieldName).join(', ')}`;
            res.status(400).json({
                error: "Errori di validazione",
                message: mainMessage,
                details: errorMessages,
                code: "VALIDATION_ERROR"
            });
            return;
        }
        await (0, typeorm_1.getRepository)(Registry_1.Registry)
            .save(registry)
            .then((registry) => {
            res.status(200).send(registry);
            return;
        })
            .catch(async (e) => {
            if (e.code === "23505") {
                // Errore di duplicato (unique constraint violation)
                console.log("Duplicate key error:", e.code);
                res.status(409).json({
                    error: "Registry già esistente",
                    message: `Esiste già un Registry con P.IVA ${registry.pIva} in questo Corporate`,
                    code: "DUPLICATE_REGISTRY",
                    details: {
                        pIva: registry.pIva,
                        corporate: registry.idCorporate
                    }
                });
                return;
            }
            else {
                // Altri errori del database - analizziamo il tipo specifico
                console.error("Database error:", e);
                let errorMessage = "Si è verificato un errore durante il salvataggio del Registry";
                let errorCode = "DATABASE_ERROR";
                let statusCode = 500;
                // Analizza errori specifici del database
                if (e.code === "23502") {
                    // NOT NULL constraint violation
                    errorMessage = "Campi obbligatori mancanti. Verificare che tutti i dati richiesti siano stati inseriti.";
                    errorCode = "MISSING_REQUIRED_FIELDS";
                    statusCode = 400;
                }
                else if (e.code === "23514") {
                    // CHECK constraint violation
                    errorMessage = "I dati inseriti non rispettano i vincoli di validità. Verificare formato e valori dei campi.";
                    errorCode = "INVALID_DATA_FORMAT";
                    statusCode = 400;
                }
                else if (e.code === "22001") {
                    // String data too long
                    errorMessage = "Uno o più campi superano la lunghezza massima consentita.";
                    errorCode = "DATA_TOO_LONG";
                    statusCode = 400;
                }
                else if (e.code === "22P02") {
                    // Invalid text representation
                    errorMessage = "Formato dati non valido. Verificare che i campi numerici contengano solo numeri.";
                    errorCode = "INVALID_DATA_TYPE";
                    statusCode = 400;
                }
                else if (e.code === "23503") {
                    // Foreign key constraint violation
                    errorMessage = "Riferimento non valido. Il Corporate specificato non esiste.";
                    errorCode = "INVALID_REFERENCE";
                    statusCode = 400;
                }
                else if (e.message && e.message.includes('timeout')) {
                    // Database timeout
                    errorMessage = "Timeout del database. Riprovare tra qualche istante.";
                    errorCode = "DATABASE_TIMEOUT";
                    statusCode = 503;
                }
                else if (e.message && e.message.includes('connection')) {
                    // Connection error
                    errorMessage = "Errore di connessione al database. Contattare l'amministratore.";
                    errorCode = "DATABASE_CONNECTION_ERROR";
                    statusCode = 503;
                }
                res.status(statusCode).json({
                    error: statusCode === 400 ? "Errore nei dati forniti" : "Errore interno del server",
                    message: errorMessage,
                    code: errorCode,
                    details: process.env.NODE_ENV === 'development' ? {
                        originalError: e.message,
                        sqlCode: e.code,
                        constraint: e.constraint
                    } : undefined
                });
                return;
            }
        });
    });
};
RegistryController.edit = async (req, res) => {
    const id = res.locals.jwtPayload.userId;
    (0, typeorm_1.getRepository)(User_1.User)
        .findOneOrFail(id, { relations: ["idRegistry", "idRegistry.idCorporate"] })
        .then(async (user) => {
        var idRegistry = parseInt(req.query.idRegistry.toString());
        // Trova il Registry da modificare
        const existingRegistry = await (0, typeorm_1.getRepository)(Registry_1.Registry).findOne(idRegistry, {
            relations: ["idCorporate"]
        });
        if (!existingRegistry) {
            res.status(404).json({
                error: "Registry non trovato",
                message: "Il Registry specificato non esiste"
            });
            return;
        }
        // Validazioni specifiche per AGENTI
        if (user.role === "AGENTE") {
            // Gli AGENTI possono modificare Registry solo del proprio Corporate
            if (existingRegistry.idCorporate.id !== user.idRegistry.idCorporate.id) {
                res.status(403).json({
                    error: "Accesso negato",
                    message: "Gli AGENTI possono modificare solo Registry del proprio Corporate"
                });
                return;
            }
            // Se viene modificata la P.IVA, validarla
            if (req.body.pIva && req.body.pIva !== existingRegistry.pIva) {
                // Validazione formato P.IVA italiana (11 cifre)
                const pIvaRegex = /^[0-9]{11}$/;
                if (!pIvaRegex.test(req.body.pIva.replace(/\s/g, ""))) {
                    res.status(400).json({
                        error: "P.IVA non valida",
                        message: "La P.IVA deve essere composta da 11 cifre"
                    });
                    return;
                }
                // Verifica che non esista già un altro registry con la stessa P.IVA nel Corporate
                const duplicateRegistry = await (0, typeorm_1.getRepository)(Registry_1.Registry).findOne({
                    where: {
                        pIva: req.body.pIva,
                        idCorporate: user.idRegistry.idCorporate,
                        id: (0, typeorm_1.Not)(idRegistry) // Esclude il registry corrente
                    }
                });
                if (duplicateRegistry) {
                    res.status(409).json({
                        error: "P.IVA già esistente",
                        message: `Esiste già un altro Registry con P.IVA ${req.body.pIva} in questo Corporate`
                    });
                    return;
                }
            }
            // Gli AGENTI non possono cambiare il Corporate
            if (req.body.idCorporate && req.body.idCorporate !== existingRegistry.idCorporate.id) {
                res.status(403).json({
                    error: "Operazione non consentita",
                    message: "Gli AGENTI non possono modificare il Corporate di appartenenza"
                });
                return;
            }
        }
        let registry = new Registry_1.Registry();
        registry = req.body;
        registry.updateAt = new Date();
        await (0, typeorm_1.getRepository)(Registry_1.Registry)
            .update({
            id: idRegistry,
        }, registry)
            .then(() => {
            res.status(200).send("modifica avvenuta");
            return;
        })
            .catch((e) => {
            console.error("Error updating registry:", e);
            if (e.code === "23505") {
                res.status(409).json({
                    error: "Conflitto dati",
                    message: "I dati forniti sono in conflitto con un Registry esistente",
                    code: "DUPLICATE_DATA",
                    details: process.env.NODE_ENV === 'development' ? e.detail : undefined
                });
            }
            else {
                res.status(500).json({
                    error: "Errore interno del server",
                    message: "Si è verificato un errore durante la modifica del Registry",
                    code: "UPDATE_ERROR",
                    details: process.env.NODE_ENV === 'development' ? e.message : undefined
                });
            }
            return;
        });
    })
        .catch((err) => {
        console.error("Error in edit method:", err);
        res.status(500).json({
            error: "Errore interno del server",
            message: "Si è verificato un errore durante l'elaborazione della richiesta",
            code: "INTERNAL_ERROR",
            details: process.env.NODE_ENV === 'development' ? err.message : undefined
        });
        return;
    });
};
RegistryController.delete = async (req, res) => {
    res.status(201).send("cancellazione non implementata");
};
RegistryController.get = async (req, res, uniqueConstraints) => {
    console.log(uniqueConstraints);
    const id = res.locals.jwtPayload.userId;
    (0, typeorm_1.getRepository)(User_1.User)
        .findOne(id, { relations: ["idRegistry", "idRegistry.idCorporate"] })
        .then(async (user) => {
        await (0, typeorm_1.getRepository)(Registry_1.Registry)
            .find({ where: { idCorporate: user.idRegistry.idCorporate } })
            .then(async (registry) => {
            var ids = registry.map((element) => element.id);
            try {
                const registryRepository = (0, typeorm_1.getRepository)(Registry_1.Registry);
                if (req.query.id != undefined) {
                    let id = parseInt(req.query.id.toString());
                    registryRepository
                        .findOne(id, {
                        where: {
                            id: (0, typeorm_1.In)(ids),
                        },
                        relations: ["users"],
                    })
                        .then((registry) => {
                        if (!registry) {
                            res.status(404).json({
                                error: "Registry non trovato",
                                message: "Il Registry specificato non esiste o non hai i permessi per accedervi",
                                code: "REGISTRY_NOT_FOUND"
                            });
                            return;
                        }
                        res.status(200).send(registry);
                    })
                        .catch((e) => {
                        console.error("Error fetching registry by ID:", e);
                        res.status(500).json({
                            error: "Errore interno del server",
                            message: "Si è verificato un errore durante il recupero del Registry",
                            code: "FETCH_ERROR",
                            details: process.env.NODE_ENV === 'development' ? e.message : undefined
                        });
                        return;
                    });
                }
                else {
                    registryRepository
                        .find({
                        where: {
                            id: (0, typeorm_1.In)(ids),
                        },
                        relations: ["users"],
                    })
                        .then((registry) => {
                        res.status(200).send(registry);
                    })
                        .catch((e) => {
                        console.error("Error fetching registries:", e);
                        res.status(500).json({
                            error: "Errore interno del server",
                            message: "Si è verificato un errore durante il recupero dei Registry",
                            code: "FETCH_ERROR",
                            details: process.env.NODE_ENV === 'development' ? e.message : undefined
                        });
                        return;
                    });
                }
            }
            catch (errore) {
                console.error("Unexpected error in get method:", errore);
                res.status(500).json({
                    error: "Errore interno del server",
                    message: "Si è verificato un errore imprevisto durante l'elaborazione della richiesta",
                    code: "UNEXPECTED_ERROR",
                    details: process.env.NODE_ENV === 'development' ? errore.message : undefined
                });
                return;
            }
        });
    });
};
RegistryController.anonimous = async (req, res) => {
    const connection = (0, typeorm_1.getConnection)();
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    let result = [];
    let { registry, user } = req.body;
    let idUserCreated = 0;
    registry.tel = registry.cellnum + "/" + registry.telnum;
    registry.isValid = true;
    registry.paymentMetod = registry.paymentMetod != "" ? registry.paymentMetod : "CONTANTE ALLO SCARICO";
    try {
        await queryRunner.manager.save(Registry_1.Registry, registry)
            .then(async (registry_created) => {
            result.push("Registry saved id: " + registry_created.id);
            let user_new = new User_1.User();
            user_new.username = user.username;
            user_new.password = user.password;
            user_new.role = "AFFILIATO";
            user_new.idRegistry = registry_created.id;
            //Validade if the parameters are ok
            const errors = await (0, class_validator_1.validate)(user_new);
            if (errors.length > 0) {
                result.push("User invalid " + errors);
            }
            user_new.hashPassword();
            await queryRunner.manager.save(User_1.User, user_new)
                .then(async (user_created) => {
                result.push("User saved id: " + user_created.id);
                idUserCreated = user_created.id;
                let affiliate = new Affiliate_1.Affiliate();
                affiliate.idRegistry = registry_created.id;
                affiliate = await queryRunner.manager.save(Affiliate_1.Affiliate, affiliate);
                let pricelistaffiliate = new PriceListAffiliate_1.PriceListAffiliate();
                pricelistaffiliate.idAffiliate = affiliate.id;
                pricelistaffiliate.idPriceList = await (await (0, typeorm_1.getRepository)(PriceList_1.PriceList).findOne()).id;
                await queryRunner.manager.save(PriceListAffiliate_1.PriceListAffiliate, pricelistaffiliate)
                    .then(async () => {
                    result.push("PriceList " + pricelistaffiliate.idPriceList + " of Affiliate " + pricelistaffiliate.idAffiliate + " saved");
                    await queryRunner.commitTransaction();
                    await (0, SenderMailController_1.sendMailNewRegistration)(user_created.idRegistry.email, "<EMAIL>", "Welcome", idUserCreated, "", req.headers.host);
                })
                    .catch(e => {
                    result.push("PriceList Affiliate not Saved " + e);
                });
                //invio notifica mail
            })
                .catch(async (err) => {
                await queryRunner.rollbackTransaction();
                result.push("User not saved " + err);
            });
        });
    }
    catch (err) {
        await queryRunner.rollbackTransaction();
        result.push("Registry not saved " + err);
    }
    finally {
        await queryRunner.release();
        res.status(200).send(result);
    }
};
exports.default = RegistryController;
//# sourceMappingURL=RegistryController.js.map