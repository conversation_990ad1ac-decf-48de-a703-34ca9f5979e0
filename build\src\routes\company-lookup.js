"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const CompanyLookupController_1 = __importDefault(require("../controllers/CompanyLookupController"));
const checkJwt_1 = require("../middlewares/checkJwt");
const router = (0, express_1.Router)();
// Endpoint per il lookup P.IVA
// GET /company-lookup?vat=*************
router.get("/", [checkJwt_1.checkJwt], CompanyLookupController_1.default.lookup);
exports.default = router;
//# sourceMappingURL=company-lookup.js.map