openapi: 3.0.1
info:
  title: Winet-Eprocuement
  description: <PERSON><PERSON><PERSON><PERSON> di risorse relative a Eprocurement
  contact:
    email: <EMAIL>
  version: 0.0.1
externalDocs:
  description: Find out more about Swagger
  url: http://swagger.io
servers:
  # Added by API Auto Mocking Plugin
  - url: http://*************:3001/
  
  # Added by API Auto Mocking Plugin
  - url: http://centralunit.viniexport.com:8087/

paths:
  /affiliate:
    get:
      tags:
        - affiliate
      summary: List all affiliate
      description: ROLE PERMISSION [DISTRIBUTORE]
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
        - name: id
          in: query
          description: The user ID for get single object
          schema:
            type: integer
          required: false
      responses:
        200:
          description: A Affiliate object
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: integer
                    description: The Affiliate ID.
                  idRegistry:
                    type: number
                    description: The Registry ID.
                  idRegistry2:
                    schema:
                    $ref: "#/components/schemas/Registry"
    put:
      tags:
        - affiliate
      summary: Update an existing affiliate
      description: ROLE PERMISSION [DISTRIBUTORE]
      responses:
        200:
          description: Modifica non implementata
    post:
      tags:
        - affiliate
      summary: Create affiliate
      description: This can only be done by admin user.
      operationId: createAffiliate
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
      requestBody:
        description: Users that needs to be update username
        content:
          "*/*":
            schema:
              required:
                - idRegistry
              type: object
              properties:
                idRegistry:
                  type: integer
        required: true
      responses:
        200:
          description: Creazione avvenuta
          content: {}
    delete:
      tags:
        - affiliate
      summary: Delete an existing affiliate
      description: Delete affiliate
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
        - name: id
          in: query
          description: The user ID for get single object
          schema:
            type: integer
          required: true
      responses:
        201:
          description: Cancellazione avvenuta
          content: {}
  /auth:
    put:
      tags:
        - auth
      summary: Update an existing user
      description: Update password
      operationId: updateUser
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
      requestBody:
        description: Users that needs to be update username
        content:
          "*/*":
            schema:
              required:
                - oldPassword
                - newPassword
              type: object
              properties:
                oldPassword:
                  type: string
                newPassword:
                  type: string
        required: true
      responses:
        200:
          description: Password changed successfully
          content: {}
        403:
          description: old password not matching
    post:
      tags:
        - auth
      summary: Auth controller for login
      description: Auth controller for login
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Login"
      responses:
        400:
          description: Password or username empty
          content: {}
        401:
          description: Username not found || Invalid password
          content: {}
      x-codegen-request-body-name: body
  /user:
    get:
      tags:
        - user
      summary: List all user or single
      description: ROLE PERMISSION [ADMIN, DISTRIBUTORE]
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
        - name: id
          in: query
          description: The user ID for get single object
          schema:
            type: integer
          required: false
      responses:
        200:
          description: The products object
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/User"
                type: object
                properties:
                  idRegistry2:
                    schema:
                    $ref: "#/components/schemas/Registry"
        400:
          description: forbidden
          content: {}
    put:
      tags:
        - user
      summary: Update an existing user
      description: ROLE PERMISSION [ADMIN, DISTRIBUTORE]
      requestBody:
        description: Users that needs to be update username
        content:
          "*/*":
            schema:
              required:
                - username
              type: object
              properties:
                username:
                  type: string
        required: true
      responses:
        200:
          description: User Modified
          content: {}
        404:
          description: User not found
          content: {}
        409:
          description: Username already in use
          content: {}
    post:
      tags:
        - user
      summary: Create user
      description: ROLE PERMISSION [ADMIN, DISTRIBUTORE]
      requestBody:
        description: The user to create.
        content:
          "*/*":
            schema:
              required:
                - idRegistry
                - password
                - role
                - username
              type: object
              properties:
                username:
                  type: string
                password:
                  type: string
                role:
                  type: string
                idRegistry:
                  type: integer
        required: false
      responses:
        default:
          description: User created
          content: {}
      x-codegen-request-body-name: user
    delete:
      tags:
        - user
      summary: Delete an existing user
      description: ROLE PERMISSION [ADMIN]
      parameters:
        - name: id
          in: query
          description: The user ID for get single object
          schema:
            type: integer
          required: true
      responses:
        200:
          description: User Modified
          content: {}
        404:
          description: User not found
          content: {}
  /corporate:
    get:
      tags:
        - corporate
      summary: List all corporate
      description: ROLE PERMISSION [ADMIN]
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
        - name: id
          in: query
          description: The user ID for get single object
          schema:
            type: integer
          required: false
      responses:
        200:
          description: ""
          content: {}
        501:
          description: forbidden
          content: {}
    put:
      tags:
        - corporate
      summary: Update an existing corporate
      description: ROLE PERMISSION [ADMIN]
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
      requestBody:
        description: Users that needs to be update username
        content:
          "*/*":
            schema:
              required:
                - corporateName
              type: object
              properties:
                corporateName:
                  type: string
        required: true
      responses:
        200:
          description: ""
          content: {}
        500:
          description: ""
          content: {}
    post:
      tags:
        - corporate
      summary: Create corporate
      description: ROLE PERMISSION [ADMIN]
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
      requestBody:
        content:
          "*/*":
            schema:
              required:
                - corporateName
              type: object
              properties:
                corporateName:
                  type: string
        required: false
      responses:
        default:
          description: ""
          content: {}
      x-codegen-request-body-name: body
    delete:
      tags:
        - corporate
      summary: Delete an existing corporate
      description: ROLE PERMISSION [ADMIN]
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
        - name: id
          in: query
          description: The user ID for get single object
          schema:
            type: integer
          required: true
      responses:
        default:
          description: Cancellazione avvenuta
          content: {}
  /externalsystems:
    get:
      tags:
        - externalsystems
      summary: List all externalsystems
      description: ROLE PERMISSION [DISTRIBUTORE]
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
        - name: id
          in: query
          description: The user ID for get single object
          schema:
            type: integer
          required: false
      responses:
        200:
          description: A externalsystems object
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: integer
                    description: The externalsystems ID.
                  externalSistemName:
                    type: string
                    description: The externalsystems name.
    put:
      tags:
        - externalsystems
      summary: Update an existing externalsystems
      description: ROLE PERMISSION [DISTRIBUTORE]
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
        - name: id
          in: query
          description: The user ID for get single object
          schema:
            type: integer
          required: true
      requestBody:
        description: Externalsystems that needs to be update
        content:
          application/json:
            schema:
              type: object
              properties:
                externalSistemName:
                  type: string
                  description: The externalsystems name.
        required: true
      responses:
        200:
          description: ""
          content: {}
        500:
          description: ""
          content: {}
      x-codegen-request-body-name: body
    post:
      tags:
        - externalsystems
      summary: Create externalsystems
      description: ROLE PERMISSION [DISTRIBUTORE]
      operationId: createExternalsystems
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
      requestBody:
        description: Externalsystems that needs to be update
        content:
          application/json:
            schema:
              type: object
              properties:
                externalSistemName:
                  type: string
                  description: The externalsystems name.
        required: true
      responses:
        200:
          description: A externalsystems object
    delete:
      tags:
        - externalsystems
      summary: Delete an existing externalsystem
      description: ROLE PERMISSION [DISTRIBUTORE]
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
        - name: id
          in: query
          description: The user ID for get single object
          schema:
            type: integer
          required: true
      responses:
        200:
          description: Cancellazione avvenuta
          content: {}
  /timeconsumingop:
    post:
      tags:
        - timeconsumingop
      summary: Create timeconsumingop
      description: ROLE PERMISSION [ADMIN]
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
        - name: command
          in: query
          description: The user ID for get single object
          schema:
            type: string
            description: Order Status
            enum:
              - readClientsAlyante
              - allignStocksMrwine
              - readProductsAlyante
              - readOrdersMail
              - readProductsMrwine
              - readClientsMrwine
          required: true
      responses:
        200:
          description: richiesta presa in carico
  /registry:
    get:
      tags:
        - registry
      summary: List all registry
      description: ROLE PERMISSION [ADMIN, DISTRIBUTORE]
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
        - name: id
          in: query
          description: The user ID for get single object
          schema:
            type: integer
          required: false
      responses:
        200:
          description: The user ID for get single object
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Registry"
    post:
      tags:
        - registry
      summary: Create new registry
      description: ROLE PERMISSION [ADMIN, DISTRIBUTORE, AFFILIATO, CHAIN, AGENTE]
      parameters:
        - in: header
          name: auth
          description: "JWT token"
          schema:
            type: string
          required: true
      requestBody:
        description: Registry data to create
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Registry"
        required: true
      responses:
        201:
          description: Registry created successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Registry"
        400:
          description: Invalid data
        409:
          description: Registry already exists
        500:
          description: Internal server error
    put:
      tags:
        - registry
      summary: Update an existing registry
      description: |
        ROLE PERMISSION [ADMIN, DISTRIBUTORE, AFFILIATO, CHAIN, AGENTE]

        Gli AGENTI possono modificare solo Registry del proprio Corporate.
        Non possono modificare il Corporate di appartenenza.
        Se modificano la P.IVA, deve essere valida (11 cifre) e non duplicata.
      parameters:
        - in: header
          name: auth
          description: "JWT token"
          schema:
            type: string
          required: true
        - name: idRegistry
          in: query
          description: ID del Registry da modificare
          schema:
            type: integer
          required: true
      requestBody:
        description: Dati del Registry da aggiornare
        content:
          application/json:
            schema:
              type: object
              properties:
                firstName:
                  type: string
                  description: Ragione sociale
                  example: "Bar Centrale MODIFICATO"
                lastName:
                  type: string
                  description: Cognome (opzionale)
                address:
                  type: string
                  description: Indirizzo
                  example: "Via Roma 456"
                city:
                  type: string
                  description: Città
                  example: "Milano"
                cap:
                  type: string
                  description: CAP
                  example: "20100"
                pIva:
                  type: string
                  description: Partita IVA (11 cifre)
                  example: "98765432109"
                email:
                  type: string
                  format: email
                  description: Email
                  example: "<EMAIL>"
                tel:
                  type: string
                  description: Telefono
                  example: "02-9876543"
                paymentMetod:
                  type: string
                  description: Metodo di pagamento
                  example: "BONIFICO BANCARIO"
            examples:
              update_basic:
                summary: Modifica dati base
                value:
                  firstName: "Bar Centrale MODIFICATO"
                  address: "Via Roma 456"
                  city: "Milano"
                  email: "<EMAIL>"
              update_piva:
                summary: Modifica P.IVA
                value:
                  pIva: "98765432109"
        required: true
      responses:
        '200':
          description: Registry modificato con successo
          content:
            text/plain:
              schema:
                type: string
                example: "modifica avvenuta"
        '400':
          description: Dati non validi
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: "P.IVA non valida"
                  message:
                    type: string
                    example: "La P.IVA deve essere composta da 11 cifre"
        '403':
          description: Accesso negato
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: "Accesso negato"
                  message:
                    type: string
                    example: "Gli AGENTI possono modificare solo Registry del proprio Corporate"
        '404':
          description: Registry non trovato
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: "Registry non trovato"
                  message:
                    type: string
                    example: "Il Registry specificato non esiste"
        '409':
          description: P.IVA già esistente
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: "P.IVA già esistente"
                  message:
                    type: string
                    example: "Esiste già un altro Registry con P.IVA 98765432109 in questo Corporate"
        '500':
          description: Errore interno del server
  /retailers:
    get:
      tags:
        - retailers
      summary: List all retailers
      description: ROLE PERMISSION [DISTRIBUTORE, AFFILIATO]
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
        - name: id
          in: query
          description: The user ID for get single object
          schema:
            type: integer
          required: false
      responses:
        200:
          description: A Affiliate object
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: integer
                    description: The Retailer ID.
                  idAffiliate:
                    type: number
                    description: The Affiliate ID.
                  idRegistry:
                    schema:
                    $ref: "#/components/schemas/Registry"
    post:
      tags:
        - retailers
      summary: Create retailers
      description: ROLE PERMISSION [DISTRIBUTORE, AFFILIATO, CHAIN, AGENTE]
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
      requestBody:
        description: Externalsystems that needs to be update
        content:
          application/json:
            schema:
              type: object
              properties:
                idAffiliate:
                  type: string
                  description: Affiliate id.
                idRegistry:
                  type: integer
                  description: Registry id
        required: true
      responses:
        200:
          description: object retailer
          content: {}
      x-codegen-request-body-name: body
  /employees:
    get:
      tags:
        - employees
      summary: List all employees
      description: Get employees
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
        - name: id
          in: query
          description: The user ID for get single object
          schema:
            type: integer
          required: false
      responses:
        200:
          description: A Affiliate object
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: integer
                    description: The Employee ID.
                  idRegistry:
                    type: number
                    description: The Registry ID.
                  idRegistry2:
                    schema:
                    $ref: "#/components/schemas/Registry"
    put:
      tags:
        - employees
      summary: Update an existing employee
      description: ROLE PERMISSION [DISTRIBUTORE]
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
        - name: id
          in: query
          description: The user ID for get single object
          schema:
            type: integer
          required: false
      requestBody:
        description: Users that needs to be update username
        content:
          "*/*":
            schema:
              required:
                - idRegistry
              type: object
              properties:
                idRegistry:
                  type: integer
        required: true
      responses:
        200:
          description: A Affiliate object
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: integer
                    description: The Employee ID.
                  idRegistry:
                    type: number
                    description: The Registry ID.
                  idRegistry2:
                    schema:
                    $ref: "#/components/schemas/Registry"
    post:
      tags:
        - employees
      summary: Create employee
      description: This can only be done by admin user or distributor.
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
      requestBody:
        description: Users that needs to be update username
        content:
          "*/*":
            schema:
              required:
                - idRegistry
              type: object
              properties:
                idRegistry:
                  type: integer
        required: true
      responses:
        200:
          description: A Affiliate object
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: integer
                    description: The Employee ID.
                  idRegistry:
                    type: number
                    description: The Registry ID.
                  idRegistry2:
                    schema:
                    $ref: "#/components/schemas/Registry"
  /warehouses:
    get:
      tags:
        - warehouses
      summary: List all warehouses
      description: ROLE PERMISSION [DISTRIBUTORE]
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
        - name: id
          in: query
          description: The warehouse ID for get single object
          schema:
            type: integer
          required: false
      responses:
        200:
          description: A Affiliate object
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: integer
                    description: The warehouse ID.
                  idCorporate:
                    type: number
                    description: The Corporate ID.
                  warehouseName:
                    type: string
                    description: The warehouse name.
    put:
      tags:
        - warehouses
      summary: Update an existing warehouse
      description: ROLE PERMISSION [DISTRIBUTORE]
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
        - name: id
          in: query
          description: The warehouse ID for get single object
          schema:
            type: integer
          required: true
      requestBody:
        description: Users that needs to be update username
        content:
          "*/*":
            schema:
              required:
                - warehouseName
              type: object
              properties:
                warehouseName:
                  type: string
        required: true
      responses:
        200:
          description: A Affiliate object
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: integer
                    description: The warehouse ID.
                  idCorporate:
                    type: number
                    description: The Corporate ID.
                  warehouseName:
                    type: string
                    description: The warehouse name.
    post:
      tags:
        - warehouses
      summary: Create warehouse
      description: ROLE PERMISSION [DISTRIBUTORE]
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
      requestBody:
        description: Users that needs to be update username
        content:
          "*/*":
            schema:
              required:
                - warehouseName
              type: object
              properties:
                warehouseName:
                  type: string
        required: true
      responses:
        200:
          description: A Affiliate object
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: integer
                    description: The warehouse ID.
                  idCorporate:
                    type: number
                    description: The Corporate ID.
                  warehouseName:
                    type: string
                    description: The warehouse name.
    delete:
      tags:
        - warehouses
      summary: Delete an existing warehouse
      description: ROLE PERMISSION [DISTRIBUTORE]
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
        - name: id
          in: query
          description: The warehouse ID for get single object
          schema:
            type: integer
          required: false
      responses:
        201:
          description: Cancellazione avvenuta
  /products:
    get:
      tags:
        - products
      summary: List all products
      description: ROLE PERMISSION [DISTRIBUTORE]
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
        - name: id
          in: query
          description: The warehouse ID for get single object
          schema:
            type: integer
          required: false
      responses:
        200:
          description: The products object
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Product"
  /pricelist:
    get:
      tags:
        - pricelist
      summary: List all pricelists
      description: ROLE PERMISSION [DISTRIBUTORE]
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
        - name: id
          in: query
          description: The pricelist ID for get single object
          schema:
            type: integer
          required: false
      responses:
        200:
          description: The pricelist object
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PriceList"
                type: object
                properties:
                  products:
                    type: array
                    items:
                      type: object
                      $ref: "#/components/schemas/Product"
    put:
      tags:
        - pricelist
      summary: Update an existing pricelist
      description: ROLE PERMISSION [DISTRIBUTORE]
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
        - name: id
          in: query
          description: The pricelist ID for get single object
          schema:
            type: integer
          required: true
      requestBody:
        description: Update pricelist
        content:
          application/json:
            schema:
              type: object
              properties:
                description:
                  type: string
                  description: pricelist description.
                validFrom:
                  type: string
                  description: date valid from
                validTo:
                  type: string
                  description: date valid to
                products:
                  type: array
                  items:
                    type: object
                    properties:
                      id:
                        type: integer
                        description: product id
                      price:
                        type: string
                        description: product price (without currencies)
        required: true
      responses:
        200:
          description: The pricelist object
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PriceList"
                type: object
                properties:
                  products:
                    schema:
                    $ref: "#/components/schemas/Product"
    post:
      tags:
        - pricelist
      summary: Create pricelist
      description: ROLE PERMISSION [DISTRIBUTORE]
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
        - name: id
          in: query
          description: The warehouse ID for get single object
          schema:
            type: integer
          required: false
      requestBody:
        description: add new pricelist
        content:
          application/json:
            schema:
              type: object
              properties:
                description:
                  type: string
                  description: pricelist description.
                validFrom:
                  type: string
                  description: date valid from
                validTo:
                  type: string
                  description: date valid to
                products:
                  type: array
                  items:
                    type: object
                    properties:
                      id:
                        type: integer
                        description: product id
                      price:
                        type: string
                        description: product price (without currencies)
        required: true
      responses:
        200:
          description: The PriceList object
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PriceList"
    delete:
      tags:
        - pricelist
      summary: Delete an existing pricelist
      description: ROLE PERMISSION [DISTRIBUTORE]
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
        - name: id
          in: query
          description: The warehouse ID for get single object
          schema:
            type: integer
          required: false
      responses:
        200:
          description: Cancellazione avvenuta
  /pricelistaffiliate:
    get:
      tags:
        - pricelistaffiliate
      summary: List all affiliate pricelist
      description: ROLE PERMISSION [DISTRIBUTORE,AFFILIATO,AGENTE]
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
        - name: idPriceList
          in: query
          description: The pricelist ID for get single object ( *required if idAffiliate is empty )
          schema:
            type: integer
          required: false
        - name: idAffiliate
          in: query
          description: The affiliate ID for get single object ( *required if idPriceList is empty )
          schema:
            type: integer
          required: false
      responses:
        200:
          description: A PriceListAffiliate object
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: integer
                    description: The pricelistaffiliate ID.
                  idPriceList:
                    type: number
                    description: The idPriceList ID.
                  idAffiliate:
                    type: number
                    description: The affiliate ID.
                  idAffiliate2:
                    type: object
                    properties:
                      id:
                        type: integer
                        description: The Affiliate ID.
                      idRegistry:
                        type: integer
                        description: The Rgistry ID.
                      idRegistry2:
                        type: object
                        $ref: "#/components/schemas/Registry"
    post:
      tags:
        - pricelistaffiliate
      summary: Create affiliate pricelist
      description: ROLE PERMISSION [DISTRIBUTORE]
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
      requestBody:
        description: add new pricelistaffiliate
        content:
          application/json:
            schema:
              type: object
              properties:
                idAffiliate:
                  type: integer
                  description: Affiliate ID.
                idPriceList:
                  type: integer
                  description: Price list ID
        required: true
      responses:
        200:
          description: Associazione effettuata
    delete:
      tags:
        - pricelistaffiliate
      summary: Delete an existing pricelist affiliate
      description: ROLE PERMISSION [DISTRIBUTORE]
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
        - name: id
          in: query
          description: The pricelistaffiliate ID
          schema:
            type: integer
          required: true
      responses:
        201:
          description: Cancellazione avvenuta
  /pricelistretailer:
    get:
      tags:
        - pricelistretailer
      summary: List all retailer pricelistretailer
      description: ROLE PERMISSION [DISTRIBUTORE,AFFILIATO,PDV]
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
        - name: idPriceList
          in: query
          description: The pricelist ID for get single object ( *required if idRetailer is empty and Role is Role is DISTRIBUTORE,AFFILIATO )
          schema:
            type: integer
          required: false
        - name: idRetailer
          in: query
          description: The retailer ID for get single object ( *required if idPriceList is empty and Role is DISTRIBUTORE,AFFILIATO )
          schema:
            type: integer
          required: false
      responses:
        200:
          description: A PriceListAffiliate object
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: integer
                    description: The pricelistaffiliate ID.
                  idPriceList:
                    type: number
                    description: The idPriceList ID.
                  idRetailer:
                    type: number
                    description: The retailer ID.
                  idPriceList2:
                    type: object
                    $ref: "#/components/schemas/PriceList"
    post:
      tags:
        - pricelistretailer
      summary: Create retailer pricelist
      description: ROLE PERMISSION [DISTRIBUTORE,AFFILIATO]
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
      requestBody:
        description: add new pricelistaffiliate
        content:
          application/json:
            schema:
              type: object
              properties:
                idRetailer:
                  type: integer
                  description: Retailer ID.
                idPriceList:
                  type: integer
                  description: Price list ID
        required: true
      responses:
        200:
          description: Associazione effettuata
          content: {}
      x-codegen-request-body-name: body
    delete:
      tags:
        - pricelistretailer
      summary: Delete an existing retailer pricelist
      description: ROLE PERMISSION [DISTRIBUTORE]
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
        - name: id
          in: query
          description: The pricelistretailer ID
          schema:
            type: integer
          required: true
      responses:
        201:
          description: Cancellazione avvenuta 
  /delivery:
    get:
      tags:
        - delivery
      summary: List all delivery
      description: ROLE PERMISSION [DISTRIBUTORE,LOGISTICA,AUTISTA]
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
        - name: id
          in: query
          description: The delivery id
          schema:
            type: integer
          required: false
      responses:
        200:
          description: A PriceListAffiliate object
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: integer
                    description: The pricelistaffiliate ID.
                  idEmployee:
                    type: number
                    description: The Employee ID.
                  idOrder:
                    type: number
                    description: The Order ID.
                  note:
                    type: string
    put:
      tags:
        - delivery
      summary: Update an existing delivery
      description: ROLE PERMISSION [DISTRIBUTORE,LOGISTICA,AUTISTA]
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
        - name: id
          in: query
          description: The delivery id
          schema:
            type: integer
          required: true
      requestBody:
        description: update delivery information
        content:
          application/json:
            schema:
              type: object
              properties:
                idEmployee:
                  type: integer
                  description: Retailer ID.
                status:
                  type: integer
                  description: Price list ID
                paymentStatus:
                  type: string
                  description: In consegna | Consegnato | Non consegnato |
                note:
                  type: string
        required: true
      responses:
        200:
          description: A PriceListAffiliate object
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: integer
                    description: The pricelistaffiliate ID.
                  idEmployee:
                    type: number
                    description: The Employee ID.
                  idOrder:
                    type: number
                    description: The Order ID.
                  note:
                    type: string
    post:
      tags:
        - delivery
      summary: Create delivery
      description: ROLE PERMISSION [DISTRIBUTORE,LOGISTICA,AUTISTA]
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
      requestBody:
        description: update delivery information
        content:
          application/json:
            schema:
              type: object
              properties:
                idEmployee:
                  type: integer
                  description: Retailer ID.
                status:
                  type: integer
                  description: Price list ID
                paymentStatus:
                  type: string
                  description: In consegna | Consegnato | Non consegnato |
                note:
                  type: string
        required: true
      responses:
        200:
          description: A PriceListAffiliate object
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: integer
                    description: The pricelistaffiliate ID.
                  idEmployee:
                    type: number
                    description: The Employee ID.
                  idOrder:
                    type: number
                    description: The Order ID.
                  note:
                    type: string
    delete:
      tags:
        - delivery
      summary: Delete an existing delivery
      description: ROLE PERMISSION [DISTRIBUTORE,LOGISTICA]
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
        - name: id
          in: query
          description: The delivery id
          schema:
            type: integer
          required: false
      responses:
        201:
          description: Cancellazione avvenuta
          content: {}
      x-codegen-request-body-name: body
  /orders:
    get:
      tags:
        - orders
      summary: List all orders
      description: ROLE PERMISSION [DISTRIBUTORE,AFFILIATO,PDV,LOGISTICA,AUTISTA]
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
        - name: id
          in: query
          description: The order id
          schema:
            type: integer
          required: false
      responses:
        200:
          description: A PriceListAffiliate object
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Order"
    put:
      tags:
        - orders
      summary: Update an existing orders
      description: ROLE PERMISSION [DISTRIBUTORE,LOGISTICA,AUTISTA]
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
        - name: id
          in: query
          description: The order id
          schema:
            type: integer
          required: false
      requestBody:
        description: update delivery information
        content:
          application/json:
            schema:
              type: object
              properties:
                deliveryDestination:
                  type: string
                  description: Retailer ID.
                status:
                  type: string
                  description: Order Status
                  enum:
                    - Bozza
                    - Registrato
                    - Approvato
                    - Assegnato
                    - In consegna
                    - Consegnato
                    - Non consegnato
                    - Annullato
                paymentStatus:
                  type: string
                  description: Payment Status
                  enum:
                    - Pagato
                    - Non Pagato
                    - Pagato parzialmente
                note:
                  type: string
                  description: Price list ID
                termsPayment:
                  type: string
                  description: In consegna | Consegnato | Non consegnato |
                total:
                  type: number
                orderDate:
                  type: string
                deliveryDate:
                  type: string
                idRetailer:
                  type: integer
                products:
                  type: array
                  items:
                    type: object
                    properties:
                      id:
                        type: integer
                      quantity:
                        type: integer
                      pcsXpackage:
                        type: string
                      colli:
                        type: integer
                      tax:
                        type: integer
                      total:
                        type: number
                      unitMeasure:
                        type: string
                      unitPrice:
                        type: number
        required: true
      responses:
        200:
          description: ordine modificato
    post:
      tags:
        - orders
      summary: Create orders
      description: ROLE PERMISSION [PDV]
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
      requestBody:
        description: update delivery information
        content:
          application/json:
            schema:
              type: object
              properties:
                deliveryDestination:
                  type: string
                  description: Retailer ID.
                status:
                  type: string
                  description: Order Status
                  enum:
                    - Bozza
                    - Registrato
                    - Approvato
                    - Assegnato
                    - In consegna
                    - Consegnato
                    - Non consegnato
                    - Annullato
                paymentStatus:
                  type: string
                  description: Payment Status
                  enum:
                    - Pagato
                    - Non Pagato
                    - Pagato parzialmente
                note:
                  type: string
                  description: Price list ID
                termsPayment:
                  type: string
                  description: In consegna | Consegnato | Non consegnato |
                total:
                  type: number
                orderDate:
                  type: string
                deliveryDate:
                  type: string
                idRetailer:
                  type: integer
                products:
                  type: array
                  items:
                    type: object
                    properties:
                      id:
                        type: integer
                      quantity:
                        type: integer
                      pcsXpackage:
                        type: string
                      colli:
                        type: integer
                      tax:
                        type: integer
                      total:
                        type: number
                      unitMeasure:
                        type: string
                      unitPrice:
                        type: number
        required: true
      responses:
        200:
          description: ordine modificato
    delete:
      tags:
        - orders
      summary: Delete an existing orders
      description: ROLE PERMISSION [DISTRIBUTORE]
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
      responses:
        201:
          description: Cancellazione avvenuta
          content: {}
      x-codegen-request-body-name: body
  /destination:
    get:
      tags:
        - destination
      summary: List all destination
      description: ALL ROLES
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
        - name: idRegistry
          in: query
          description: The register id
          schema:
            type: integer
          required: true
      responses:
        200:
          description: A destination object
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Destination"
    put:
      tags:
        - destination
      summary: Update an existing destination
      description: ALL ROLES
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
        - name: idDestination
          in: query
          description: The destination id
          schema:
            type: integer
          required: true
      requestBody:
        description: update delivery information
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Destination"
        required: true
      responses:
        200:
          description: destinazione aggiornata
        500:
          description: generic error
    post:
      tags:
        - destination
      summary: Create destination
      description: ALL ROLES
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
      requestBody:
        description: create destination information
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Destination"
        required: true
      responses:
        200:
          description: destinazione aggiunta
        500:
          description: generic error
    delete:
      tags:
        - destination
      summary: Delete an existing destination
      description: ALL ROLES
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
        - name: idDestination
          in: query
          description: The destination id
          schema:
            type: integer
          required: true
      responses:
        201:
          description: Cancellazione avvenuta
          content: {}
        500:
          description: generic error
      x-codegen-request-body-name: body
  /asset/prodotti/{idProduct}.jpg:
    get:
      tags:
        - product image
      summary: image product
      description: no auth
      parameters:
        - name: idProduct
          in: path
          description: The product id
          schema:
            type: integer
          required: true
      responses:
        200:
          description: product image in JPG format
          content:
            image/jpg:
              schema:
                type: string
                format: binary
  /alyante/importcliddt: 
    get:
      tags:
        - alyante
      summary: Download CLI_DDT from alyante
      description: ROLE PERMISSION [DISTRIBUTORE,RESP_MAG,LOGISTICA]
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
        - name: idWarehouses
          in: query
          description: Id Warehouse
          schema:
            type: integer
          required: true
      responses:
        200:
          description: Result 
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: array
                    items: 
                      type: string
                  error:
                    type: array
                    items:
                      type: string
  /alyante/exportforddt: 
    get:
      tags:
        - alyante
      summary: Invio ad alyante FOR_DDT
      description: ROLE PERMISSION [DISTRIBUTORE, RESP_MAG]
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
        - in: query
          name: idWarehouses
          description: Id Warehouse
          schema:
            type: integer
          required: true
        - name: documentType
          in: query
          description: Accept DDT or FATT
          schema:
            type: string
          required: true
      responses:
        200:
          description: Not yet developed
        500:
          description: Missing Id Document or documentType wrong
        501:
          description: Error of Catch
  /document:
    get:
      tags: 
        - Document
      summary: List Document
      description: ROLE PERMISSION [DISTRIBUTORE, RESP_MAG, LOGISTICA,AFFILIATO, PDV, AGENTE]
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
        - in: query
          name: idWarehouses
          description: Id Warehouse
          schema:
            type: integer
          required: false
        - in: query
          name: idDocumentHead
          description: Id DocumentHead
          schema:
            type: integer
          required: false
        - in: query
          name: documentType
          description: Type of Document *Required if inevaso = true
          schema:
            type: string
          required: false
        - in: query
          name: inevaso
          description: List Document Outstanding *If True documentType <> FOR_*
          schema:
            type: boolean
          required: false
        - in: query
          name: idRetailer
          description: Id of Reailer
          schema:
            type: integer
          required: false
      responses:
        200:
          description: If idDocument <> null return Object else Array of Object
    delete:
      tags:
        - Document
      summary: Delete Document
      description: ROLE PERMISSION [DISTRIBUTORE, RESP_MAG]
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
        - in: query
          name: idDocumentHead
          description: Id of Document
          schema:
            type: integer
          required: true
        
      responses:
        201:
          description: String "Committed"
        501:
          description: String Error.Code
    put:
      tags:
        - Document
      summary: Update an existing Document
      description: Update Document
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
        - in: query
          name: idDocumentHead
          description: "token"
          schema:
            type: string
          required: true
      requestBody:
        description: Document to be update
        content:
          application/json:
            schema:
              required:
                - number
                - type
                - documentDate
                - rowBody
              type: object
              properties:
                number:
                  type: string
                type:
                  type: string
                documentDate:
                  type: string
                rowBody:
                  $ref: "#/components/schemas/DocumentBody"
                    
        required: true
      responses:
        200:
          description: Log of Operation
        501:
          description: Rollback with error
    post:
      tags:
        - Document
      description: Create Documnent
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
        - in: query
          name: idWarehouses
          description: Id Warehouse
          schema:
            type: integer
          required: true
        - in: query
          name: idDocument
          description: Id of Document *"Required if want make CLI_RESIDUI document"
          schema:
            type: integer
          required: false
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/DocumentHead"
              required:
                - rowBody
                - idRetailer
              properties:
                rowBody:
                  $ref: "#/components/schemas/DocumentBody"
                idRetailer:
                  $ref: "#/components/schemas/Retailers"
      responses:
        200:
          description: Document Created Return array with {'result:'string,'exception':string}
        501:
          description: Document not Created return array with exception
        500:
          description: Id Warehouse Missing     
  /document/transformorder:
    get:
      tags:
        - Document
      summary: Trasform order to Document
      description: ROLE PERMISSION [DISTRIBUTORE]
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
        - in: query
          name: idOrder
          schema:
            type: integer
          description: Id of Order
          required: true
        - in: query
          name: idWarehouse
          schema:
            type: integer
          description: Id of Warehouse
          required: true
      responses:
        200:
          description: Array with log operation
        500:
          description: Order just trasformed
        501:
          description: Rollback with error    
  /document/unifydocument:
    get:
      tags:
        - Document
      summary: Unify Document
      description: ROLE PERMISSION [DISTRIBUTORE]
      parameters:
        - in: header
          name: auth
          description: "token"
          schema:
            type: string
          required: true
        - in: query
          name: idWarehouses
          schema:
            type: integer
          description: Id of Warehouse
          required: true
        - in: body
          name: body
          description: List idDocument to unify.
          schema:
            type: object
            required:
              - document
            properties:
              document:
                type: array
                items: 
                  type: integer
          required: true
      responses:
        200:
          description: Object with {"result:" log operation,"error:" error}
        501:
          description: Documents not Found
        500:
          description: IdWarehouse Missing
            
          
          
  
  /agent/pdv:
    post:
      tags:
        - agent
      summary: Crea un nuovo PDV per un AGENTE
      description: |
        Permette agli AGENTI di creare autonomamente nuovi punti vendita (PDV).
        L'agente può creare PDV solo nel proprio Corporate.
        ROLE PERMISSION [AGENTE]
      operationId: createPDVForAgent
      parameters:
        - in: header
          name: auth
          description: "JWT token"
          schema:
            type: string
          required: true
      requestBody:
        description: Dati del nuovo PDV da creare
        content:
          application/json:
            schema:
              type: object
              required:
                - ragioneSociale
                - pIva
              properties:
                ragioneSociale:
                  type: string
                  description: Ragione sociale del PDV
                  example: "Bar Centrale di Mario Rossi"
                  maxLength: 255
                pIva:
                  type: string
                  description: Partita IVA (11 cifre)
                  example: "12345678901"
                  pattern: "^[0-9]{11}$"
                codiceFiscale:
                  type: string
                  description: Codice fiscale (opzionale, se diverso dalla P.IVA)
                  example: "****************"
                indirizzo:
                  type: string
                  description: Indirizzo del PDV
                  example: "Via Roma, 123"
                  maxLength: 255
                cap:
                  type: string
                  description: Codice di Avviamento Postale
                  example: "20100"
                  pattern: "^[0-9]{5}$"
                citta:
                  type: string
                  description: Città
                  example: "Milano"
                  maxLength: 100
                provincia:
                  type: string
                  description: Provincia (2 lettere maiuscole)
                  example: "MI"
                  pattern: "^[A-Z]{2}$"
                telefono:
                  type: string
                  description: Numero di telefono
                  example: "+39 02 1234567"
                email:
                  type: string
                  format: email
                  description: Indirizzo email
                  example: "<EMAIL>"
                  maxLength: 100
                pec:
                  type: string
                  format: email
                  description: Indirizzo PEC
                  example: "<EMAIL>"
                  maxLength: 100
        required: true
      responses:
        201:
          description: PDV creato con successo
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "PDV creato con successo"
                  data:
                    type: object
                    properties:
                      registry:
                        $ref: "#/components/schemas/Registry"
                      retailer:
                        $ref: "#/components/schemas/Retailer"
                      createdBy:
                        type: object
                        properties:
                          userId:
                            type: integer
                          username:
                            type: string
                          role:
                            type: string
        400:
          description: Dati non validi
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: "Dati non validi"
                  message:
                    type: string
                    example: "I dati forniti non sono validi"
                  details:
                    type: array
                    items:
                      type: string
                    example: ["P.IVA deve essere composta da 11 cifre"]
        403:
          description: Accesso negato
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: "Accesso negato"
                  message:
                    type: string
                    example: "Solo gli AGENTI possono utilizzare questo endpoint"
        409:
          description: PDV già esistente
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: "PDV già esistente"
                  message:
                    type: string
                    example: "Esiste già un PDV con P.IVA 12345678901 in questo Corporate"
        500:
          description: Errore interno del server
    get:
      tags:
        - agent
      summary: Lista i PDV dell'agente
      description: |
        Restituisce tutti i PDV creati dall'agente corrente.
        ROLE PERMISSION [AGENTE]
      operationId: getMyPDV
      parameters:
        - in: header
          name: auth
          description: "JWT token"
          schema:
            type: string
          required: true
      responses:
        200:
          description: Lista PDV dell'agente
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      affiliate:
                        type: object
                        properties:
                          id:
                            type: integer
                          registry:
                            $ref: "#/components/schemas/Registry"
                      pdvCount:
                        type: integer
                        example: 5
                      pdvList:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: integer
                            registry:
                              $ref: "#/components/schemas/Registry"
                            createdAt:
                              type: string
                              format: date-time
                            updatedAt:
                              type: string
                              format: date-time
        403:
          description: Accesso negato
        500:
          description: Errore interno del server

  /company-lookup:
    get:
      tags:
        - company-lookup
      summary: Ricerca dati aziendali tramite P.IVA
      description: |
        Utilizza il servizio VIES (VAT Information Exchange System) dell'Unione Europea
        per ricercare automaticamente i dati aziendali tramite Partita IVA italiana.

        **Servizio**: VIES - Gratuito
        **Formato P.IVA**: IT + 11 cifre (es: *************)
        **Timeout**: 10 secondi
      parameters:
        - in: header
          name: Authorization
          description: "Bearer JWT token"
          schema:
            type: string
          required: true
        - name: vat
          in: query
          description: |
            Partita IVA italiana nel formato IT + 11 cifre.
            Esempi: *************, IT 07231500963 (spazi rimossi automaticamente)
          schema:
            type: string
            pattern: '^IT[0-9]{11}$'
            example: "*************"
          required: true
      responses:
        200:
          description: Dati aziendali trovati con successo
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  company:
                    type: object
                    properties:
                      name:
                        type: string
                        description: Ragione sociale dell'azienda
                        example: "AMAZON ITALIA SERVICES SRL"
                      vatNumber:
                        type: string
                        description: Partita IVA completa
                        example: "*************"
                      address:
                        type: string
                        description: Indirizzo stradale
                        example: "VIALE MONTE GRAPPA 3/5"
                      city:
                        type: string
                        description: Città
                        example: "MILANO MI"
                      postalCode:
                        type: string
                        description: Codice postale (se disponibile)
                        example: "20124"
                      country:
                        type: string
                        description: Codice paese
                        example: "IT"
                      isValid:
                        type: boolean
                        description: Validità della P.IVA
                        example: true
                  source:
                    type: string
                    description: Fonte dei dati
                    example: "VIES"
        400:
          description: Errore nei parametri di input
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  error:
                    type: object
                    properties:
                      code:
                        type: string
                        enum: [MISSING_VAT_PARAMETER, INVALID_VAT_FORMAT]
                        example: "INVALID_VAT_FORMAT"
                      message:
                        type: string
                        example: "Formato Partita IVA non valido. Utilizzare il formato IT + 11 cifre"
        401:
          description: Token di autenticazione mancante o non valido
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: "Authentication Error"
                  message:
                    type: string
                    example: "Access token is required"
        404:
          description: P.IVA non trovata nel database VIES
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  error:
                    type: object
                    properties:
                      code:
                        type: string
                        example: "VAT_NOT_FOUND"
                      message:
                        type: string
                        example: "Nessuna azienda trovata con questa Partita IVA"
        503:
          description: Servizio VIES temporaneamente non disponibile
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  error:
                    type: object
                    properties:
                      code:
                        type: string
                        enum: [SERVICE_UNAVAILABLE, SERVICE_TIMEOUT]
                        example: "SERVICE_UNAVAILABLE"
                      message:
                        type: string
                        example: "Il servizio di lookup P.IVA è temporaneamente non disponibile"
        500:
          description: Errore interno del server
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  error:
                    type: object
                    properties:
                      code:
                        type: string
                        example: "INTERNAL_ERROR"
                      message:
                        type: string
                        example: "Errore interno del server durante il lookup P.IVA"

components:
  schemas:
    Registry:
      type: object
      properties:
        id:
          type: integer
        firstName:
          type: integer
        lastName:
          type: integer
        address:
          type: string
        city:
          type: string
        pIva:
          type: string
        email:
          type: string
        tel:
          type: string
        isValid:
          type: boolean
        externalCode:
          type: integer
        cap:
          type: string
          description: Order Status
          enum:
            - placed
            - approved
            - delivered
    Order:
      type: object
      properties:
        id:
          type: integer
        deliveryDestination:
          type: integer
          format: int64
        status:
          type: string
          description: Order Status
          enum:
            - Bozza
            - Registrato
            - Approvato
            - Assegnato
            - In consegna
            - Consegnato
            - Non consegnato
            - Annullato
        payment_status:
          type: string
          description: Payment Status
          enum:
            - Pagato
            - Non Pagato
            - Pagato parzialmente
        note:
          type: string
        termsPayment:
          type: string
        total:
          type: string
        totalTaxed:
          type: string
        orderNumber:
          type: string
        orderDate:
          type: string
        deliveryDate:
          type: string
    DocumentHead:
      type: object
      properties:
        id:
          type: integer
        type:
          type: string
          format: varchar
          description: Type of Document
          enum:
            - CLI_ORDER
            - FOR_ORDER
            - CLI_DDT
            - FOR_DDT
            - INVENTORY
            - CLI_RESIDUI
        document_date:
          type: string
          format: timestamp
          description: Order Status
          enum:
            - Bozza
            - Registrato
            - Approvato
            - Assegnato
            - In consegna
            - Consegnato
            - Non consegnato
            - Annullato
        id_registry_creator:
          type: integer
          description: the id of registry
        create_at:
          type: string
          format: timestamp
        id_registry_update:
          type: integer
        update_at:
          type: string
          format: timestamp
          description: Date
        id_warehouse:
          type: integer
        number:
          type: string
        id_supplying:
          type: integer
        id_retailer:
          type: integer
        total:
          type: number
        total_taxed:
          type: number
        total_payed:
          type: number
        deliveryStatus:
          type: string
        note:
          type: string
        delivery_date :
          type: string
          format: timestamp
        erp_sync: 
          type: boolean
        delivery_destination:
          type: string
    DocumentBody:
      type: object
      properties:
        id:
          type: integer
        id_products_packaging:
           $ref: "#/components/schemas/ProductsPackaging"
        ean_code:
          type: string
          description: EanCode of Product
        colli_preventivo:
          type: integer
          description: Expected Quantity
        colli_consuntivo:
          type: integer
          description: Quatity Found
        lotto:
          type: string
          description: Batch product
        id_document:
          $ref: "#/components/schemas/DocumentHead"       
    User:
      type: object
      properties:
        id:
          type: integer
          format: int64
        username:
          type: string
        password:
          type: string
        role:
          type: string
        idRegistry:
          type: integer
          description: the id of registry
    Product:
      type: object
      properties:
        id:
          type: integer
        description:
          type: string
        nationality:
          type: string
        region:
          type: string
        family:
          type: string
        subfamily:
          type: string
        group:
          type: string
        subgroup:
          type: string
        format:
          type: string
        deposit:
          type: string
        status:
          type: string
        brand:
          type: string
        eanCode:
          type: string
        iva:
          type: number
        externalCode:
          type: string
        supplyingCode:
          type: string
        idRegistry:
          type: integer
          description: the id of registry
    PriceList:
      type: object
      properties:
        id:
          type: integer
        idCorporate:
          type: integer
        description:
          type: string
        validFrom:
          type: string
        validTo:
          type: string
    Login:
      properties:
        username:
          type: string
        password:
          type: string
    Destination:
      type: object
      properties:
        destcap:
          type: string
        destcitta:
          type: string
        destemail:
          type: string
        destind:
          type: string
        destprov:
          type: string
        destragsoc:
          type: string
        desttel:
          type: string
        idRegistry:
          type: integer
    ProductsPackaging:
      type: object
      properties:
        id:
          type: integer
        unit_measure:
          type: string
        pcs_x_package:
          type: integer
        create_at:
          type: string
          format: timestamp
        update_at:
          type: string
          format: timestamp
        id_product:
          $ref: "#/components/schemas/Product"
        ean_code:
          type: string
        id_supplyin_products:
          type: integer
    Retailers:
      type: object
      properties:
        id:
          type: integer
        id_affiliate:
          $ref: "#/components/schemas/Affiliate"
        created_at:
          type: string
          format: Timestamp
        update_at:
          type: string
        id_registry:
          $ref: "#/components/schemas/Registry"
    Affiliate:
      type: object
      properties:
        id:
          type: integer
        created_at:
          type: string
          format: Timestamp
        update_at:
          type: string
        id_registry:
          $ref: "#/components/schemas/Registry"
    Retailer:
      type: object
      properties:
        id:
          type: integer
          description: ID univoco del retailer
        idAffiliate:
          type: integer
          description: ID dell'affiliate associato
        createdAt:
          type: string
          format: date-time
          description: Data di creazione
        updateAt:
          type: string
          format: date-time
          description: Data di ultimo aggiornamento
        idRegistry:
          $ref: "#/components/schemas/Registry"
          description: Anagrafica associata al retailer
