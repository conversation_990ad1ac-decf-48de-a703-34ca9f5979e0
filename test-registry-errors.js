/**
 * Test per verificare la gestione degli errori migliorata del Registry Controller
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3001';

async function testRegistryErrors() {
  console.log('🧪 Testing Registry Error Handling...\n');

  // Test 1: Errore di validazione usando endpoint anonimo (dovrebbe essere 400, non 501)
  console.log('1. Testing validation error with anonymous endpoint...');
  try {
    const response = await axios.post(`${BASE_URL}/registry/anonimous`, {
      // Dati mancanti per causare errore di validazione
      firstName: '', // Campo vuoto
      pIva: 'invalid' // P.IVA non valida
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    if (error.response) {
      console.log(`   Status: ${error.response.status}`);
      console.log(`   Error: ${JSON.stringify(error.response.data, null, 2)}`);
      
      if (error.response.status === 400) {
        console.log('   ✅ Validation error correctly returns 400');
      } else if (error.response.status === 501) {
        console.log('   ❌ Still returning 501 for validation errors');
      } else {
        console.log(`   ⚠️  Unexpected status code: ${error.response.status}`);
      }
    } else {
      console.log(`   ❌ Network error: ${error.message}`);
    }
  }

  console.log('\n2. Testing authentication error structure...');
  try {
    const response = await axios.get(`${BASE_URL}/registry`);
    console.log(`   Status: ${response.status}`);
    console.log(`   ✅ GET request successful (unexpected - should require auth)`);
  } catch (error) {
    if (error.response) {
      console.log(`   Status: ${error.response.status}`);
      console.log(`   Error: ${JSON.stringify(error.response.data, null, 2)}`);

      if (error.response.status === 401) {
        console.log('   ✅ Correctly returns 401 for missing authentication');
      }

      if (error.response.data.error && error.response.data.message) {
        console.log('   ✅ Authentication error has proper structure');
      } else {
        console.log('   ❌ Authentication error missing proper structure');
      }
    } else {
      console.log(`   ❌ Network error: ${error.message}`);
    }
  }

  console.log('\n3. Testing non-existent registry...');
  try {
    const response = await axios.get(`${BASE_URL}/registry?id=99999`);
    console.log(`   Status: ${response.status}`);
  } catch (error) {
    if (error.response) {
      console.log(`   Status: ${error.response.status}`);
      console.log(`   Error: ${JSON.stringify(error.response.data, null, 2)}`);
      
      if (error.response.status === 404) {
        console.log('   ✅ Non-existent registry correctly returns 404');
      } else {
        console.log(`   ⚠️  Unexpected status for non-existent registry: ${error.response.status}`);
      }
    } else {
      console.log(`   ❌ Network error: ${error.message}`);
    }
  }

  console.log('\n📊 Test Summary:');
  console.log('   - Validation errors should return 400 (not 501)');
  console.log('   - Duplicate errors should return 409 (not 501)');
  console.log('   - Database errors should return 500 (not 501)');
  console.log('   - Not found errors should return 404');
  console.log('   - All errors should have structured JSON with error codes');
}

// Esegui i test
testRegistryErrors().catch(console.error);
